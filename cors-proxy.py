#!/usr/bin/env python3
"""
本地 CORS 代理服务器
用于解决跨域访问问题
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.request
import urllib.parse
import json
import sys
from urllib.error import URLError, HTTPError

class CORSProxyHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """处理 GET 请求"""
        try:
            # 健康检查端点
            if self.path == '/health':
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps({'status': 'ok', 'message': 'CORS proxy is running'}).encode())
                print("✅ 健康检查请求")
                return
            
            # 解析请求路径
            if self.path.startswith('/proxy/'):
                target_url = self.path[7:]  # 移除 '/proxy/' 前缀
            else:
                self.send_error(400, "Invalid proxy path")
                return
            
            # 构建目标 URL
            if not target_url.startswith('http'):
                target_url = 'https://' + target_url
            
            print(f"🔄 代理请求: {target_url}")
            
            # 创建请求
            req = urllib.request.Request(target_url)
            req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            
            # 发送请求
            with urllib.request.urlopen(req, timeout=10) as response:
                content = response.read()
                content_type = response.headers.get('Content-Type', 'application/json')
                
                # 发送响应
                self.send_response(200)
                self.send_header('Content-Type', content_type)
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, HEAD')
                self.send_header('Access-Control-Allow-Headers', 'Content-Type, Accept, Cache-Control, User-Agent')
                self.end_headers()
                self.wfile.write(content)
                
                print(f"✅ 代理成功: {len(content)} bytes")
                
        except HTTPError as e:
            print(f"❌ HTTP 错误: {e.code} - {e.reason}")
            self.send_error(e.code, f"Proxy error: {e.reason}")
        except URLError as e:
            print(f"❌ URL 错误: {e.reason}")
            self.send_error(500, f"Proxy error: {e.reason}")
        except Exception as e:
            print(f"❌ 未知错误: {e}")
            self.send_error(500, f"Proxy error: {str(e)}")
    
    def do_OPTIONS(self):
        """处理 OPTIONS 请求（预检请求）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, HEAD')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Accept, Cache-Control, User-Agent')
        self.end_headers()
    
    def do_HEAD(self):
        """处理 HEAD 请求"""
        try:
            # 解析请求路径
            if self.path.startswith('/proxy/'):
                target_url = self.path[7:]  # 移除 '/proxy/' 前缀
            else:
                self.send_error(400, "Invalid proxy path")
                return
            
            # 构建目标 URL
            if not target_url.startswith('http'):
                target_url = 'https://' + target_url
            
            print(f"🔄 代理HEAD请求: {target_url}")
            
            # 创建请求
            req = urllib.request.Request(target_url, method='HEAD')
            req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            
            # 发送请求
            with urllib.request.urlopen(req, timeout=10) as response:
                # 发送响应头
                self.send_response(200)
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, HEAD')
                self.send_header('Access-Control-Allow-Headers', 'Content-Type, Accept, Cache-Control, User-Agent')
                self.end_headers()
                
                print(f"✅ HEAD请求成功")
                
        except HTTPError as e:
            print(f"❌ HTTP 错误: {e.code} - {e.reason}")
            self.send_error(e.code, f"Proxy error: {e.reason}")
        except URLError as e:
            print(f"❌ URL 错误: {e.reason}")
            self.send_error(500, f"Proxy error: {e.reason}")
        except Exception as e:
            print(f"❌ 未知错误: {e}")
            self.send_error(500, f"Proxy error: {str(e)}")
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"📝 {format % args}")

def start_proxy_server(port=8080):
    """启动代理服务器"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, CORSProxyHandler)
    
    print(f"🚀 CORS 代理服务器已启动")
    print(f"🌐 代理地址: http://localhost:{port}")
    print(f"📝 使用方法: http://localhost:{port}/proxy/v2202406227732275300.nicesrv.de:5000/your/api/path")
    print(f"📄 比赛查询页面: http://localhost:8000/match-query.html")
    print("\n按 Ctrl+C 停止服务器")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 代理服务器已停止")
        httpd.server_close()

if __name__ == "__main__":
    port = 8080
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("❌ 端口号必须是数字")
            sys.exit(1)
    
    start_proxy_server(port) 