/**
 * 用户列表 API 服务模块 v2.0
 * 负责获取用户最后在线时间和状态
 * 使用降级策略：直接请求 -> 本地代理 -> 流式API -> 公共代理
 */

console.log('🚀 用户列表 API 模块 v2.0 已加载 - 包含完整降级策略和流式API支持');

// 用户数据缓存
const userDataCache = new Map();

/**
 * 处理API错误
 * @param {Error} error - 错误对象
 * @param {string} steamId - Steam ID
 * @returns {Error} 处理后的错误
 */
function handleUserListApiError(error, steamId) {
    console.error(`获取用户 ${steamId} 数据失败:`, error);
    
    // 更精确的错误分类
    if (error.message.includes('CORS') || error.message.includes('Failed to fetch')) {
        return new Error('网络连接失败，请检查网络连接');
    } else if (error.message.includes('timeout') || error.message.includes('abort')) {
        return new Error('请求超时，请检查网络连接');
    } else if (error.message.includes('404')) {
        return new Error('未找到用户数据');
    } else if (error.message.includes('500')) {
        return new Error('服务器内部错误');
    } else if (error.message.includes('数据解析失败')) {
        return new Error('数据格式错误，请稍后重试');
    } else if (error.message.includes('API响应失败')) {
        return new Error('API服务暂时不可用，请稍后重试');
    } else {
        return new Error(`请求失败：${error.message}`);
    }
}

/**
 * 获取单个用户的最后一场比赛记录 - 使用降级策略 + 流式API
 * @param {string} steamId - Steam ID
 * @param {number} retryCount - 当前重试次数
 * @returns {Promise<Object|null>} 最后一场比赛数据
 */
async function getUserLastMatch(steamId, retryCount = 0) {
    console.log(`🔍 获取用户 ${steamId} 的最后比赛记录 (重试: ${retryCount})`);
    
    const controller = new AbortController();
    const { TIMEOUT } = window.STREAMING_API_CONFIG;
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT);
    
    // 将URL定义移到函数开头，避免作用域问题
    const streamingUrl = window.USER_LIST_API_ENDPOINTS.RECENT_MATCHES(steamId);
    
    try {
        // 1. 尝试直接请求
        console.log(`📡 尝试直接请求: ${streamingUrl}`);
        let response = await fetch(streamingUrl, {
            signal: controller.signal,
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });
        clearTimeout(timeoutId);
        
        if (!response.ok) {
            throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log(`✅ 直接请求成功，获取到用户 ${steamId} 的数据`);
        
        const lastMatch = extractLastMatchFromData(data);
        if (lastMatch) {
            console.log(`✅ 成功提取用户 ${steamId} 的最后比赛`);
            return lastMatch;
        } else {
            console.warn(`⚠️ 用户 ${steamId} 没有有效的比赛数据`);
            return null;
        }
        
    } catch (error) {
        clearTimeout(timeoutId);
        console.error('直接请求失败，尝试使用本地 CORS 代理:', error);
        
        // 2. 尝试本地CORS代理
        try {
            const localProxy = 'http://localhost:8080/proxy/';
            const proxyUrl = `${localProxy}${streamingUrl.replace('https://', '')}`;
            console.log(`📡 本地代理URL: ${proxyUrl}`);
            
            const proxyResponse = await fetch(proxyUrl, {
                signal: controller.signal,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });
            
            if (!proxyResponse.ok) {
                throw new Error(`本地代理失败: ${proxyResponse.status}`);
            }
            
            const data = await proxyResponse.json();
            console.log(`✅ 本地代理成功，获取到用户 ${steamId} 的数据`);
            
            const lastMatch = extractLastMatchFromData(data);
            if (lastMatch) {
                console.log(`✅ 成功提取用户 ${steamId} 的最后比赛`);
                return lastMatch;
            } else {
                console.warn(`⚠️ 用户 ${steamId} 没有有效的比赛数据`);
                return null;
            }
            
        } catch (proxyError) {
            console.error('本地 CORS 代理请求也失败:', proxyError);
            
            // 3. 尝试流式API
            try {
                console.log(`🌊 尝试流式API获取用户 ${steamId} 数据`);
                const streamData = await getUserLastMatchStreaming(steamId, controller);
                if (streamData) {
                    console.log(`✅ 流式API成功，获取到用户 ${steamId} 的数据`);
                    return streamData;
                }
            } catch (streamError) {
                console.error('流式API也失败:', streamError);
            }
            
            // 4. 尝试公共CORS代理
            try {
                const publicProxy = 'https://cors-anywhere.herokuapp.com/';
                const publicUrl = `${publicProxy}${streamingUrl}`;
                console.log(`📡 公共代理URL: ${publicUrl}`);
                
                const publicResponse = await fetch(publicUrl, {
                    signal: controller.signal,
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!publicResponse.ok) {
                    throw new Error(`公共代理失败: ${publicResponse.status}`);
                }
                
                const data = await publicResponse.json();
                console.log(`✅ 公共代理成功，获取到用户 ${steamId} 的数据`);
                
                const lastMatch = extractLastMatchFromData(data);
                if (lastMatch) {
                    console.log(`✅ 成功提取用户 ${steamId} 的最后比赛`);
                    return lastMatch;
                } else {
                    console.warn(`⚠️ 用户 ${steamId} 没有有效的比赛数据`);
                    return null;
                }
                
            } catch (publicError) {
                console.error('公共 CORS 代理请求也失败:', publicError);
                
                // 所有方法都失败了，进行重试逻辑
                const { MAX_RETRIES, RETRY_DELAY_BASE } = window.STREAMING_API_CONFIG;
                const isRetryableError = isRetryableErrorType(error);
                
                if (isRetryableError && retryCount < MAX_RETRIES) {
                    console.log(`🔄 可重试错误，${retryCount + 1}/${MAX_RETRIES} 次重试: ${error.message}`);
                    
                    // 指数退避重试
                    const delay = Math.pow(2, retryCount) * RETRY_DELAY_BASE;
                    await new Promise(resolve => setTimeout(resolve, delay));
                    
                    return getUserLastMatch(steamId, retryCount + 1);
                }
                
                // 最终失败，记录错误
                const handledError = handleUserListApiError(error, steamId);
                console.error(`❌ 获取用户 ${steamId} 比赛记录最终失败: ${handledError.message}`);
                return null;
            }
        }
    }
}

/**
 * 使用流式API获取用户最后比赛记录
 * @param {string} steamId - Steam ID
 * @param {AbortController} controller - 中止控制器
 * @returns {Promise<Object|null>} 最后比赛记录
 */
async function getUserLastMatchStreaming(steamId, controller) {
    const streamTimeout = setTimeout(() => {
        console.log('⏰ 流式API请求超时，中止请求');
        controller.abort();
    }, 15000); // 15秒超时
    
    try {
        const streamingUrl = window.USER_LIST_API_ENDPOINTS.RECENT_MATCHES(steamId);
        console.log(`🌊 开始流式API请求: ${streamingUrl}`);
        
        const response = await fetch(streamingUrl, {
            signal: controller.signal
        });
        
        if (!response.ok) {
            throw new Error(`流式API请求失败: ${response.status} ${response.statusText}`);
        }
        
        // 检查是否支持流式读取
        const reader = response.body?.getReader();
        if (!reader) {
            throw new Error('响应不支持流式读取');
        }
        
        console.log('📡 开始流式处理比赛数据...');
        const data = await processStreamingDataSimple(reader, steamId);
        
        if (data && (data.matches || data.match_stats || Array.isArray(data))) {
            const lastMatch = extractLastMatchFromData(data);
            if (lastMatch) {
                console.log(`✅ 流式API成功提取最后比赛`);
                return lastMatch;
            }
        }
        
        console.warn(`⚠️ 流式API未找到有效比赛数据`);
        return null;
        
    } catch (error) {
        console.error('流式API处理失败:', error);
        return null;
    } finally {
        clearTimeout(streamTimeout);
    }
}

/**
 * 简化的流式数据处理
 * @param {ReadableStreamDefaultReader} reader - 流读取器
 * @param {string} steamId - Steam ID
 * @returns {Promise<object>} 处理后的比赛数据
 */
async function processStreamingDataSimple(reader, steamId) {
    const decoder = new TextDecoder();
    let buffer = '';
    let chunkCount = 0;
    const maxChunks = 100; // 最大读取块数，防止无限循环
    
    try {
        while (chunkCount < maxChunks) {
            const { done, value } = await reader.read();
            chunkCount++;
            
            if (done) {
                console.log(`📡 流式数据读取完成，共读取 ${chunkCount} 个数据块`);
                break;
            }
            
            buffer += decoder.decode(value, { stream: true });
            
            // 每读取10个块尝试解析一次
            if (chunkCount % 10 === 0) {
                try {
                    const data = JSON.parse(buffer);
                    if (data && (Array.isArray(data) || data.matches || data.match_stats)) {
                        console.log(`✅ 流式数据解析成功，获取到 ${Array.isArray(data) ? data.length : (data.matches?.length || data.match_stats?.length || 0)} 条记录`);
                        return data;
                    }
                } catch (parseError) {
                    // 继续读取更多数据
                }
            }
        }
        
        // 最终尝试解析
        if (buffer.trim()) {
            try {
                const data = JSON.parse(buffer);
                console.log(`✅ 最终流式数据解析成功`);
                return data;
            } catch (finalError) {
                console.error('最终流式数据解析失败:', finalError);
                console.log('📄 原始数据片段:', buffer.substring(0, 200) + '...');
            }
        }
        
    } catch (error) {
        console.error('流式数据读取错误:', error);
    } finally {
        reader.releaseLock();
    }
    
    return null;
}

/**
 * 判断错误类型是否可重试
 * @param {Error} error - 错误对象
 * @returns {boolean} 是否可重试
 */
function isRetryableErrorType(error) {
    const retryableErrors = [
        'AbortError', // 超时
        'TypeError', // 网络错误
        'Failed to fetch', // 网络连接失败
        'timeout', // 超时相关
        'network', // 网络相关
        'CORS' // CORS错误
    ];
    
    // 检查错误名称和消息
    const isRetryable = retryableErrors.some(errorType => 
        error.name === errorType || 
        error.message.includes(errorType)
    );
    
    // 额外的检查：网络相关错误
    if (error.message.includes('timeout') || 
        error.message.includes('network') || 
        error.message.includes('connection')) {
        return true;
    }
    
    return isRetryable;
}

/**
 * 从数据中提取最新的比赛记录 - 优化版本
 * @param {Object} data - API返回的数据
 * @returns {Object|null} 最新比赛记录
 */
function extractLastMatchFromData(data) {
    try {
        let matches = [];
        
        // 处理不同的数据格式
        if (Array.isArray(data)) {
            matches = data;
        } else if (data && Array.isArray(data.match_stats)) {
            // 处理 {match_stats: [...]} 格式
            matches = data.match_stats.map(stat => ({
                ...stat,
                endTime: stat.date,
                matchId: stat.match_id,
                elo: stat.elo
            }));
        } else if (data && Array.isArray(data.matches)) {
            matches = data.matches;
        } else if (data && typeof data === 'object') {
            // 可能是单个比赛记录
            matches = [data];
        }
        
        if (matches.length === 0) {
            return null;
        }
        
        // 既然API返回的第一条就是最新比赛，直接取第一条
        const firstMatch = matches[0];
        const endTime = extractEndTime(firstMatch);
        
        if (!endTime) {
            return null;
        }
        
        return {
            endTime: new Date(endTime).toISOString(),
            matchId: firstMatch.MatchId || firstMatch.matchId || firstMatch.match_id || firstMatch.id,
            map: firstMatch.Map || firstMatch.map,
            result: firstMatch.Result || firstMatch.result,
            elo: firstMatch.elo
        };
        
    } catch (error) {
        console.error('❌ 提取比赛数据失败:', error);
        return null;
    }
}

/**
 * 从比赛数据中提取结束时间 - 优化版本
 * @param {Object} match - 比赛数据
 * @returns {number|null} 时间戳
 */
function extractEndTime(match) {
    try {
        // 优先使用最常用的时间字段，减少遍历次数
        const priorityFields = ['endTime', 'date', 'EndTime', 'Date'];
        
        for (const field of priorityFields) {
            if (match[field]) {
                const time = new Date(match[field]).getTime();
                if (!isNaN(time)) {
                    return time;
                }
            }
        }
        
        // 如果优先字段没有找到，再尝试其他字段
        const fallbackFields = ['MatchEndTime', 'matchEndTime', 'Timestamp', 'timestamp', 'Time', 'time'];
        for (const field of fallbackFields) {
            if (match[field]) {
                const time = new Date(match[field]).getTime();
                if (!isNaN(time)) {
                    return time;
                }
            }
        }
        
        return null;
    } catch (error) {
        console.error('❌ 提取时间失败:', error);
        return null;
    }
}

/**
 * 批量获取用户最后在线时间 - 逐个串行版本
 * @param {Array} users - 用户列表
 * @param {Function} onProgress - 进度回调
 * @param {boolean} forceRefresh - 是否强制刷新
 * @returns {Promise<Array>} 包含在线时间的用户列表
 */
async function getUsersLastOnlineTime(users, onProgress = null, forceRefresh = false) {
    console.log(`🚀 开始逐个获取 ${users.length} 个用户的在线时间 (串行模式)`);
    
    const results = [];
    const { REQUEST_DELAY, CACHE_DURATION } = window.USER_LIST_CONFIG;
    
    // 检查缓存
    const now = Date.now();
    const usersToFetch = forceRefresh ? users : users.filter(user => {
        const cached = userDataCache.get(user.steamId);
        return !cached || (now - cached.timestamp) > CACHE_DURATION;
    });
    
    console.log(`📊 需要获取 ${usersToFetch.length} 个用户数据，${users.length - usersToFetch.length} 个使用缓存`);
    
    // 处理缓存的用户
    for (const user of users) {
        if (!usersToFetch.includes(user)) {
            const cached = userDataCache.get(user.steamId);
            if (cached) {
                results.push({
                    ...user,
                    lastOnlineTime: cached.lastOnlineTime,
                    status: cached.status,
                    lastMatch: cached.lastMatch
                });
            }
        }
    }
    
    // 逐个串行处理用户
    let completed = users.length - usersToFetch.length;
    
    for (let i = 0; i < usersToFetch.length; i++) {
        const user = usersToFetch[i];
        const userIndex = i + 1;
        
        try {
            console.log(`📡 正在获取用户 ${user.name} (${userIndex}/${usersToFetch.length})`);
            console.log(`🔍 用户详情: ${user.name} (${user.steamId})`);
            
            const lastMatch = await getUserLastMatch(user.steamId);
            const lastOnlineTime = extractLastOnlineTime(lastMatch);
            const status = determineUserStatus(lastOnlineTime);
            
            const userData = {
                ...user,
                lastOnlineTime,
                status,
                lastMatch
            };
            
            // 更新缓存
            userDataCache.set(user.steamId, {
                lastOnlineTime,
                status,
                lastMatch,
                timestamp: now
            });
            
            results.push(userData);
            completed++;
            
            console.log(`✅ 用户 ${user.name} 获取完成 - 状态: ${status}, 最后在线: ${lastOnlineTime ? lastOnlineTime.toLocaleString() : '未知'}`);
            
            // 更新进度
            if (onProgress) {
                onProgress(completed, users.length);
            }
            
            // 请求间隔，避免对服务器造成压力
            if (i < usersToFetch.length - 1) {
                console.log(`⏳ 等待 ${REQUEST_DELAY / 1000} 秒后处理下一个用户...`);
                await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY));
            }
            
        } catch (error) {
            console.error(`❌ 获取用户 ${user.name} (${user.steamId}) 数据失败:`, error);
            
            // 即使失败也添加到结果中，避免阻塞后续用户
            results.push({
                ...user,
                lastOnlineTime: null,
                status: 'unknown',
                lastMatch: null
            });
            
            completed++;
            if (onProgress) {
                onProgress(completed, users.length);
            }
            
            // 失败后也等待一段时间再继续
            if (i < usersToFetch.length - 1) {
                console.log(`⏳ 用户获取失败，等待 ${REQUEST_DELAY / 1000} 秒后继续...`);
                await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY));
            }
        }
    }
    
    console.log(`✅ 逐个获取完成，共处理 ${results.length} 个用户`);
    return results.sort((a, b) => a.name.localeCompare(b.name));
}

/**
 * 从比赛数据中提取最后在线时间
 * @param {Object|null} lastMatch - 最后一场比赛数据
 * @returns {Date|null} 最后在线时间
 */
function extractLastOnlineTime(lastMatch) {
    if (!lastMatch || !lastMatch.endTime) {
        return null;
    }
    
    try {
        return new Date(lastMatch.endTime);
    } catch (error) {
        console.error('❌ 解析在线时间失败:', error);
        return null;
    }
}

/**
 * 根据最后比赛时间判断用户状态
 * @param {Date|null} lastOnlineTime - 最后在线时间
 * @returns {string} 用户状态 ('online', 'recent', 'offline', 'unknown')
 */
function determineUserStatus(lastOnlineTime) {
    if (!lastOnlineTime) {
        return 'unknown';
    }
    
    const now = new Date();
    const diffHours = (now - lastOnlineTime) / (1000 * 60 * 60);
    const { RECENT_ACTIVE_HOURS } = window.USER_LIST_CONFIG;
    
    if (diffHours < 1) {
        return 'online';
    } else if (diffHours < RECENT_ACTIVE_HOURS) {
        return 'recent';
    } else {
        return 'offline';
    }
}

// 导出函数到全局作用域
window.UserListAPI = {
    getUserLastMatch,
    getUsersLastOnlineTime,
    extractLastMatchFromData,
    extractLastOnlineTime,
    determineUserStatus,
    userDataCache
};

console.log('✅ 用户列表 API 模块已加载（包含CORS代理支持）');