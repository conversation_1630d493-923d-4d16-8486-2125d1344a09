// 单位映射配置
const UNIT_MAPPING = {
    // 步兵单位
    82: { name: '步兵', category: 'infantry' },
    84: { name: '机枪手', category: 'infantry' },
    85: { name: '狙击手', category: 'infantry' },
    86: { name: '火箭兵', category: 'infantry' },
    87: { name: '医疗兵', category: 'infantry' },
    88: { name: '工程师', category: 'infantry' },
    89: { name: '侦察兵', category: 'infantry' },
    90: { name: '突击兵', category: 'infantry' },
    91: { name: '重装兵', category: 'infantry' },
    92: { name: '特种兵', category: 'infantry' },
    
    // 车辆单位
    320: { name: '主战坦克', category: 'vehicle' },
    321: { name: '轻型坦克', category: 'vehicle' },
    322: { name: '自行火炮', category: 'vehicle' },
    323: { name: '装甲运兵车', category: 'vehicle' },
    324: { name: '防空车', category: 'vehicle' },
    325: { name: '工程车', category: 'vehicle' },
    326: { name: '侦察车', category: 'vehicle' },
    327: { name: '运输车', category: 'vehicle' },
    328: { name: '指挥车', category: 'vehicle' },
    329: { name: '维修车', category: 'vehicle' },
    
    // 空中单位
    448: { name: '战斗机', category: 'air' },
    449: { name: '轰炸机', category: 'air' },
    450: { name: '攻击机', category: 'air' },
    451: { name: '运输机', category: 'air' },
    452: { name: '直升机', category: 'air' },
    453: { name: '侦察机', category: 'air' },
    454: { name: '预警机', category: 'air' },
    455: { name: '加油机', category: 'air' },
    
    // 支援单位
    600: { name: '雷达站', category: 'support' },
    601: { name: '通信站', category: 'support' },
    602: { name: '补给站', category: 'support' },
    603: { name: '医疗站', category: 'support' },
    604: { name: '维修站', category: 'support' },
    605: { name: '指挥中心', category: 'support' },
    
    // 防御单位
    700: { name: '防空炮', category: 'defense' },
    701: { name: '反坦克炮', category: 'defense' },
    702: { name: '机枪碉堡', category: 'defense' },
    703: { name: '地雷', category: 'defense' },
    704: { name: '铁丝网', category: 'defense' },
    705: { name: '壕沟', category: 'defense' },
    706: { name: '掩体', category: 'defense' },
    707: { name: '观察哨', category: 'defense' },
    
    // 海军单位
    800: { name: '驱逐舰', category: 'navy' },
    801: { name: '巡洋舰', category: 'navy' },
    802: { name: '战列舰', category: 'navy' },
    803: { name: '航空母舰', category: 'navy' },
    804: { name: '潜艇', category: 'navy' },
    805: { name: '护卫舰', category: 'navy' },
    806: { name: '补给舰', category: 'navy' },
    807: { name: '扫雷舰', category: 'navy' }
};

// 分类颜色映射
const CATEGORY_COLORS = {
    infantry: '#28a745',    // 绿色
    vehicle: '#dc3545',     // 红色
    air: '#17a2b8',         // 蓝色
    navy: '#20c997',        // 青色
    support: '#ffc107',     // 黄色
    defense: '#6f42c1'      // 紫色
};

// 分类名称映射
const CATEGORY_NAMES = {
    infantry: '步兵',
    vehicle: '车辆',
    air: '空中',
    navy: '海军',
    support: '支援',
    defense: '防御'
};

// 获取单位名称
function getUnitName(unitId) {
    const unit = UNIT_MAPPING[unitId];
    return unit ? unit.name : `单位${unitId}`;
}

// 获取单位分类
function getUnitCategory(unitId) {
    const unit = UNIT_MAPPING[unitId];
    return unit ? unit.category : 'unknown';
}

// 获取分类名称
function getCategoryName(category) {
    return CATEGORY_NAMES[category] || category;
}

// 获取分类颜色
function getCategoryColor(category) {
    return CATEGORY_COLORS[category] || '#6c757d';
}

// 导出函数（如果支持模块）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        UNIT_MAPPING,
        CATEGORY_COLORS,
        CATEGORY_NAMES,
        getUnitName,
        getUnitCategory,
        getCategoryName,
        getCategoryColor
    };
}
