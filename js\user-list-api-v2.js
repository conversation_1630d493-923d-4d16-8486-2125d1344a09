/**
 * 用户列表 API 服务模块 v2.4 - 修复时间解析
 * 负责获取用户最后在线时间和状态
 * 使用成功的流式JSON处理和正确的时间解析
 */

console.log('🚀 用户列表 API 模块 v2.4 - 修复时间解析已加载！');
console.log('📋 包含成功的流式JSON处理和正确的时间字段解析');
console.log('🔄 降级顺序：流式API -> 直接请求 -> 本地代理 -> 公共代理');
console.log('⏰ 时间字段优先级：EndTime -> date -> 其他字段');

// 用户数据缓存
const userDataCache = new Map();

/**
 * 处理API错误
 * @param {Error} error - 错误对象
 * @param {string} steamId - Steam ID
 * @returns {Error} 处理后的错误
 */
function handleUserListApiError(error, steamId) {
    console.error(`获取用户 ${steamId} 数据失败:`, error);
    
    // 更精确的错误分类
    if (error.message.includes('CORS') || error.message.includes('Failed to fetch')) {
        return new Error('网络连接失败，请检查网络连接');
    } else if (error.message.includes('timeout') || error.message.includes('abort')) {
        return new Error('请求超时，请检查网络连接');
    } else if (error.message.includes('404')) {
        return new Error('未找到用户数据');
    } else if (error.message.includes('500')) {
        return new Error('服务器内部错误');
    } else if (error.message.includes('数据解析失败')) {
        return new Error('数据格式错误，请稍后重试');
    } else if (error.message.includes('API响应失败')) {
        return new Error('API服务暂时不可用，请稍后重试');
    } else {
        return new Error(`请求失败：${error.message}`);
    }
}

/**
 * 获取单个用户的最后一场比赛记录 - 基于index.html成功经验
 * @param {string} steamId - Steam ID
 * @param {number} retryCount - 当前重试次数
 * @returns {Promise<Object|null>} 最后一场比赛数据
 */
async function getUserLastMatch(steamId, retryCount = 0) {
    console.log(`🔍 获取用户 ${steamId} 的最后比赛记录 (重试: ${retryCount})`);
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    try {
        // 1. 尝试流式API（基于index.html的成功经验）
        console.log(`🌊 尝试流式API获取用户 ${steamId} 数据`);
        const streamData = await getUserLastMatchStreaming(steamId, controller);
        if (streamData) {
            console.log(`✅ 流式API成功，获取到用户 ${steamId} 的数据`);
            return streamData;
        }
        
    } catch (error) {
        console.error('流式API失败，尝试降级策略:', error);
    }
    
    // 2. 尝试直接请求
    try {
        const streamingUrl = window.USER_LIST_API_ENDPOINTS.RECENT_MATCHES(steamId);
        console.log(`📡 尝试直接请求: ${streamingUrl}`);
        
        const response = await fetch(streamingUrl, {
            signal: controller.signal,
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });
        clearTimeout(timeoutId);
        
        if (!response.ok) {
            throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log(`✅ 直接请求成功，获取到用户 ${steamId} 的数据`);
        
        const lastMatch = extractLastMatchFromData(data);
        if (lastMatch) {
            console.log(`✅ 成功提取用户 ${steamId} 的最后比赛`);
            return lastMatch;
        } else {
            console.warn(`⚠️ 用户 ${steamId} 没有有效的比赛数据`);
            return null;
        }
        
    } catch (error) {
        clearTimeout(timeoutId);
        console.error('直接请求失败，尝试使用本地 CORS 代理:', error);
        
        // 3. 尝试本地CORS代理
        try {
            const localProxy = 'http://localhost:8080/proxy/';
            const streamingUrl = window.USER_LIST_API_ENDPOINTS.RECENT_MATCHES(steamId);
            const proxyUrl = `${localProxy}${streamingUrl.replace('https://', '')}`;
            console.log(`📡 本地代理URL: ${proxyUrl}`);
            
            const proxyController = new AbortController();
            const proxyTimeoutId = setTimeout(() => proxyController.abort(), 10000);
            
            const proxyResponse = await fetch(proxyUrl, {
                signal: proxyController.signal,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });
            clearTimeout(proxyTimeoutId);
            
            if (!proxyResponse.ok) {
                throw new Error(`本地代理失败: ${proxyResponse.status}`);
            }
            
            const data = await proxyResponse.json();
            console.log(`✅ 本地代理成功，获取到用户 ${steamId} 的数据`);
            
            const lastMatch = extractLastMatchFromData(data);
            if (lastMatch) {
                console.log(`✅ 成功提取用户 ${steamId} 的最后比赛`);
                return lastMatch;
            } else {
                console.warn(`⚠️ 用户 ${steamId} 没有有效的比赛数据`);
                return null;
            }
            
        } catch (proxyError) {
            console.error('本地 CORS 代理请求也失败:', proxyError);
            
            // 4. 尝试公共CORS代理
            try {
                const publicProxy = 'https://cors-anywhere.herokuapp.com/';
                const streamingUrl = window.USER_LIST_API_ENDPOINTS.RECENT_MATCHES(steamId);
                const publicUrl = `${publicProxy}${streamingUrl}`;
                console.log(`📡 公共代理URL: ${publicUrl}`);
                
                const publicController = new AbortController();
                const publicTimeoutId = setTimeout(() => publicController.abort(), 10000);
                
                const publicResponse = await fetch(publicUrl, {
                    signal: publicController.signal,
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                clearTimeout(publicTimeoutId);
                
                if (!publicResponse.ok) {
                    throw new Error(`公共代理失败: ${publicResponse.status}`);
                }
                
                const data = await publicResponse.json();
                console.log(`✅ 公共代理成功，获取到用户 ${steamId} 的数据`);
                
                const lastMatch = extractLastMatchFromData(data);
                if (lastMatch) {
                    console.log(`✅ 成功提取用户 ${steamId} 的最后比赛`);
                    return lastMatch;
                } else {
                    console.warn(`⚠️ 用户 ${steamId} 没有有效的比赛数据`);
                    return null;
                }
                
            } catch (publicError) {
                console.error('公共 CORS 代理请求也失败:', publicError);
                
                // 所有方法都失败了，进行重试逻辑
                const { MAX_RETRIES, RETRY_DELAY_BASE } = window.STREAMING_API_CONFIG;
                const isRetryableError = isRetryableErrorType(error);
                
                if (isRetryableError && retryCount < MAX_RETRIES) {
                    console.log(`🔄 可重试错误，${retryCount + 1}/${MAX_RETRIES} 次重试: ${error.message}`);
                    
                    // 指数退避重试
                    const delay = Math.pow(2, retryCount) * RETRY_DELAY_BASE;
                    await new Promise(resolve => setTimeout(resolve, delay));
                    
                    return getUserLastMatch(steamId, retryCount + 1);
                }
                
                // 最终失败，记录错误
                const handledError = handleUserListApiError(error, steamId);
                console.error(`❌ 获取用户 ${steamId} 比赛记录最终失败: ${handledError.message}`);
                return null;
            }
        }
    }
}

/**
 * 使用流式API获取用户最后比赛记录 - 基于index.html成功经验
 * @param {string} steamId - Steam ID
 * @param {AbortController} controller - 中止控制器
 * @returns {Promise<Object|null>} 最后比赛记录
 */
async function getUserLastMatchStreaming(steamId, controller) {
    const streamTimeout = setTimeout(() => {
        console.log('⏰ 流式API请求超时，中止请求');
        controller.abort();
    }, 15000); // 15秒超时
    
    try {
        const streamingUrl = window.USER_LIST_API_ENDPOINTS.RECENT_MATCHES(steamId);
        console.log(`🌊 开始流式API请求: ${streamingUrl}`);
        
        const response = await fetch(streamingUrl, {
            signal: controller.signal
        });
        
        if (!response.ok) {
            throw new Error(`流式API请求失败: ${response.status} ${response.statusText}`);
        }
        
        // 检查是否支持流式读取
        const reader = response.body?.getReader();
        if (!reader) {
            throw new Error('响应不支持流式读取');
        }
        
        console.log('📡 开始流式处理比赛数据...');
        const data = await processStreamingDataSimple(reader, steamId);
        
        if (data && data.matches && data.matches.length > 0) {
            const lastMatch = extractLastMatchFromData(data);
            if (lastMatch) {
                console.log(`✅ 流式API成功提取最后比赛`);
                return lastMatch;
            }
        }
        
        console.warn(`⚠️ 流式API未找到有效比赛数据`);
        return null;
        
    } catch (error) {
        console.error('流式API处理失败:', error);
        return null;
    } finally {
        clearTimeout(streamTimeout);
    }
}

/**
 * 简化的流式数据处理 - 基于index.html成功经验
 * @param {ReadableStreamDefaultReader} reader - 流读取器
 * @param {string} steamId - Steam ID
 * @returns {Promise<object>} 处理后的比赛数据
 */
async function processStreamingDataSimple(reader, steamId) {
    const decoder = new TextDecoder();
    let matches = [];
    let buffer = '';
    let chunkCount = 0;
    let totalBytes = 0;
    let parseErrors = 0;
    let successfulParses = 0;
    
    console.log('🚀 开始流式数据处理，连接到API端点');
    
    try {
        while (true) {
            const { done, value } = await reader.read();
            if (done) {
                console.log('🏁 流式数据读取完成');
                break;
            }
            
            chunkCount++;
            totalBytes += value.length;
            
            // 将新数据追加到缓冲区
            const chunk = decoder.decode(value, { stream: true });
            buffer += chunk;
            
            console.log(`📦 接收数据块 #${chunkCount}: ${value.length} 字节，累计: ${totalBytes.toLocaleString()} 字节`);
            
            // 尝试从缓冲区中提取完整的 JSON 对象
            let lines = buffer.split('\n');
            
            // 保留最后一行（可能不完整）
            const lastLine = lines.pop() || '';
            buffer = lastLine;
            
            for (const line of lines) {
                const trimmedLine = line.trim();
                if (!trimmedLine) continue;
                
                try {
                    // 尝试解析JSON
                    const data = JSON.parse(trimmedLine);
                    successfulParses++;
                    console.log(`✅ JSON解析成功: ${data.type}`);
                    
                    // 处理不同类型的流式数据
                    switch (data.type) {
                        case "metadata":
                            console.log(`🎯 收到元数据: ${data.totalMatches || 0} 场比赛，Steam ID: ${data.steamId}`);
                            break;
                            
                        case "match":
                            if (data.data) {
                                matches.push(data.data);
                                console.log(`🎮 处理比赛数据 ${matches.length} - 比赛ID: ${data.data.fightId || 'N/A'}`);
                                
                                // 如果已经有足够的比赛数据，提前返回
                                if (matches.length >= 5) {
                                    console.log(`✅ 已获取足够的比赛数据，提前结束`);
                                    return { matches: matches };
                                }
                            }
                            break;
                            
                        case "complete":
                            console.log(`✅ 数据传输完成，总计处理 ${data.totalProcessed || matches.length} 场比赛`);
                            return { matches: matches };
                            
                        case "error":
                            console.error(`❌ 服务器错误: ${data.error}`);
                            throw new Error(data.error);
                            
                        default:
                            console.log(`⚠️ 未知消息类型: ${data.type}`);
                    }
                    
                } catch (parseErr) {
                    parseErrors++;
                    console.error(`❌ JSON解析失败: ${parseErr.message}`);
                    console.log(`   原始数据片段: ${trimmedLine.substring(0, 100)}${trimmedLine.length > 100 ? '...' : ''}`);
                    
                    // 智能恢复策略
                    if (trimmedLine.includes('{') && !trimmedLine.includes('}')) {
                        console.log('🔄 检测到不完整JSON对象，添加到缓冲区等待更多数据');
                        buffer = trimmedLine + (buffer ? '\n' + buffer : '');
                    } else if (trimmedLine.startsWith(':') || trimmedLine.startsWith(',') || trimmedLine.startsWith('"')) {
                        console.log('🔄 检测到JSON片段，可能是数据截断，跳过此片段');
                        continue;
                    }
                }
            }
            
            // 防止缓冲区过大
            if (buffer.length > 100000) {
                console.log('⚠️ 缓冲区过大，进行清理以防止内存溢出');
                const lastBraceIndex = buffer.lastIndexOf('{');
                if (lastBraceIndex > 0) {
                    buffer = buffer.substring(lastBraceIndex);
                    console.log('🧹 缓冲区已清理，保留最后的JSON对象');
                } else {
                    buffer = '';
                    console.log('🧹 缓冲区已完全清空');
                }
            }
        }
        
        // 处理缓冲区中剩余的数据
        if (buffer.trim()) {
            try {
                const data = JSON.parse(buffer.trim());
                if (data.type === "match" && data.data) {
                    matches.push(data.data);
                    console.log(`🎮 处理缓冲区中的比赛数据: ${data.data.fightId || 'N/A'}`);
                }
            } catch (parseErr) {
                console.log(`⚠️ 缓冲区剩余数据解析失败: ${parseErr.message}`);
            }
        }
        
        console.log(`🏆 流式数据处理完成！统计信息:`);
        console.log(`   📊 总比赛数: ${matches.length}`);
        console.log(`   📦 数据块数: ${chunkCount}`);
        console.log(`   💾 总字节数: ${totalBytes.toLocaleString()}`);
        console.log(`   ✅ 解析成功: ${successfulParses}`);
        console.log(`   ❌ 解析失败: ${parseErrors}`);
        
        return { matches: matches };
        
    } catch (error) {
        console.error(`💥 流式处理发生严重错误: ${error.message}`);
        throw error;
    } finally {
        reader.releaseLock();
        console.log('🔓 流式读取器已释放，连接已关闭');
    }
}

/**
 * 解析流式JSON数据（每行一个JSON对象）
 * @param {string} buffer - 原始数据
 * @returns {Array} 解析后的比赛数据数组
 */
function parseStreamingJSON(buffer) {
    const lines = buffer.split('\n').filter(line => line.trim());
    const matches = [];
    
    for (const line of lines) {
        try {
            const jsonObj = JSON.parse(line);
            
            // 只处理比赛数据
            if (jsonObj.type === 'match' && jsonObj.data) {
                matches.push(jsonObj.data);
            }
        } catch (error) {
            console.warn('⚠️ 跳过无效的JSON行:', line.substring(0, 50) + '...');
        }
    }
    
    return matches;
}

/**
 * 判断错误类型是否可重试
 * @param {Error} error - 错误对象
 * @returns {boolean} 是否可重试
 */
function isRetryableErrorType(error) {
    const retryableErrors = [
        'AbortError', // 超时
        'TypeError', // 网络错误
        'Failed to fetch', // 网络连接失败
        'timeout', // 超时相关
        'network', // 网络相关
        'CORS' // CORS错误
    ];
    
    // 检查错误名称和消息
    const isRetryable = retryableErrors.some(errorType => 
        error.name === errorType || 
        error.message.includes(errorType)
    );
    
    // 额外的检查：网络相关错误
    if (error.message.includes('timeout') || 
        error.message.includes('network') || 
        error.message.includes('connection')) {
        return true;
    }
    
    return isRetryable;
}

/**
 * 从数据中提取最新的比赛记录 - 优化版本
 * @param {Object} data - API返回的数据
 * @returns {Object|null} 最新比赛记录
 */
function extractLastMatchFromData(data) {
    try {
        let matches = [];
        
        // 处理不同的数据格式
        if (Array.isArray(data)) {
            matches = data;
        } else if (data && Array.isArray(data.match_stats)) {
            // 处理 {match_stats: [...]} 格式
            matches = data.match_stats.map(stat => ({
                ...stat,
                endTime: stat.date,
                matchId: stat.match_id,
                elo: stat.elo
            }));
        } else if (data && Array.isArray(data.matches)) {
            matches = data.matches;
        } else if (data && typeof data === 'object') {
            // 可能是单个比赛记录
            matches = [data];
        }
        
        if (matches.length === 0) {
            return null;
        }
        
        // 既然API返回的第一条就是最新比赛，直接取第一条
        const firstMatch = matches[0];
        const endTime = extractEndTime(firstMatch);
        
        if (!endTime) {
            return null;
        }
        
        return {
            endTime: new Date(endTime).toISOString(),
            matchId: firstMatch.MatchId || firstMatch.matchId || firstMatch.match_id || firstMatch.id,
            map: firstMatch.Map || firstMatch.map,
            result: firstMatch.Result || firstMatch.result,
            elo: firstMatch.elo
        };
        
    } catch (error) {
        console.error('❌ 提取比赛数据失败:', error);
        return null;
    }
}

/**
 * 从比赛数据中提取结束时间 - 修复版本，支持流式数据格式
 * @param {Object} match - 比赛数据
 * @returns {number|null} 时间戳
 */
function extractEndTime(match) {
    try {
        console.log(`🔍 开始提取时间，比赛数据:`, {
            fightId: match.fightId,
            EndTime: match.EndTime,
            date: match.date,
            endTime: match.endTime
        });
        
        // 优先使用流式数据格式的EndTime字段（大写E）
        if (match.EndTime !== undefined && match.EndTime !== null) {
            console.log(`✅ 找到EndTime字段: ${match.EndTime}, 类型: ${typeof match.EndTime}`);
            
            let time;
            if (typeof match.EndTime === 'number') {
                // EndTime是Unix时间戳（秒），需要转换为毫秒
                time = match.EndTime * 1000;
                console.log(`🔄 转换EndTime时间戳: ${match.EndTime}s -> ${time}ms`);
            } else if (typeof match.EndTime === 'string') {
                time = new Date(match.EndTime).getTime();
                console.log(`🔄 解析EndTime字符串: ${match.EndTime} -> ${time}ms`);
            }
            
            if (time && !isNaN(time)) {
                console.log(`✅ EndTime解析成功: ${new Date(time).toISOString()}`);
                return time;
            } else {
                console.warn(`⚠️ EndTime解析失败: ${match.EndTime}`);
            }
        }
        
        // 其次使用date字段
        if (match.date !== undefined && match.date !== null) {
            console.log(`✅ 找到date字段: ${match.date}, 类型: ${typeof match.date}`);
            
            let time;
            if (typeof match.date === 'number') {
                // 检查是否是秒级时间戳
                if (match.date < 10000000000) { // 秒级时间戳
                    time = match.date * 1000;
                    console.log(`🔄 转换date时间戳: ${match.date}s -> ${time}ms`);
                } else { // 毫秒级时间戳
                    time = match.date;
                    console.log(`🔄 使用date毫秒时间戳: ${time}ms`);
                }
            } else if (typeof match.date === 'string') {
                time = new Date(match.date).getTime();
                console.log(`🔄 解析date字符串: ${match.date} -> ${time}ms`);
            }
            
            if (time && !isNaN(time)) {
                console.log(`✅ date解析成功: ${new Date(time).toISOString()}`);
                return time;
            } else {
                console.warn(`⚠️ date解析失败: ${match.date}`);
            }
        }
        
        // 最后尝试其他字段
        const fallbackFields = ['endTime', 'EndTime', 'MatchEndTime', 'matchEndTime', 'Timestamp', 'timestamp', 'Time', 'time'];
        for (const field of fallbackFields) {
            if (match[field] !== undefined && match[field] !== null) {
                console.log(`✅ 找到${field}字段: ${match[field]}, 类型: ${typeof match[field]}`);
                
                let time;
                if (typeof match[field] === 'number') {
                    // 检查是否是秒级时间戳
                    if (match[field] < 10000000000) { // 秒级时间戳
                        time = match[field] * 1000;
                        console.log(`🔄 转换${field}时间戳: ${match[field]}s -> ${time}ms`);
                    } else { // 毫秒级时间戳
                        time = match[field];
                        console.log(`🔄 使用${field}毫秒时间戳: ${time}ms`);
                    }
                } else if (typeof match[field] === 'string') {
                    time = new Date(match[field]).getTime();
                    console.log(`🔄 解析${field}字符串: ${match[field]} -> ${time}ms`);
                }
                
                if (time && !isNaN(time)) {
                    console.log(`✅ ${field}解析成功: ${new Date(time).toISOString()}`);
                    return time;
                } else {
                    console.warn(`⚠️ ${field}解析失败: ${match[field]}`);
                }
            }
        }
        
        console.error(`❌ 未找到有效的时间字段`);
        return null;
    } catch (error) {
        console.error('❌ 提取时间失败:', error);
        return null;
    }
}

/**
 * 批量获取用户最后在线时间 - 逐个串行版本
 * @param {Array} users - 用户列表
 * @param {Function} onProgress - 进度回调
 * @param {boolean} forceRefresh - 是否强制刷新
 * @returns {Promise<Array>} 包含在线时间的用户列表
 */
async function getUsersLastOnlineTime(users, onProgress = null, forceRefresh = false) {
    console.log(`🚀 开始逐个获取 ${users.length} 个用户的在线时间 (串行模式)`);
    
    const results = [];
    const { REQUEST_DELAY, CACHE_DURATION } = window.USER_LIST_CONFIG;
    
    // 检查缓存
    const now = Date.now();
    const usersToFetch = forceRefresh ? users : users.filter(user => {
        const cached = userDataCache.get(user.steamId);
        return !cached || (now - cached.timestamp) > CACHE_DURATION;
    });
    
    console.log(`📊 需要获取 ${usersToFetch.length} 个用户数据，${users.length - usersToFetch.length} 个使用缓存`);
    
    // 处理缓存的用户
    for (const user of users) {
        if (!usersToFetch.includes(user)) {
            const cached = userDataCache.get(user.steamId);
            if (cached) {
                results.push({
                    ...user,
                    lastOnlineTime: cached.lastOnlineTime,
                    status: cached.status,
                    lastMatch: cached.lastMatch
                });
            }
        }
    }
    
    // 逐个串行处理用户
    let completed = users.length - usersToFetch.length;
    
    for (let i = 0; i < usersToFetch.length; i++) {
        const user = usersToFetch[i];
        const userIndex = i + 1;
        
        try {
            console.log(`📡 正在获取用户 ${user.name} (${userIndex}/${usersToFetch.length})`);
            console.log(`🔍 用户详情: ${user.name} (${user.steamId})`);
            
            const lastMatch = await getUserLastMatch(user.steamId);
            const lastOnlineTime = extractLastOnlineTime(lastMatch);
            const status = determineUserStatus(lastOnlineTime);
            
            const userData = {
                ...user,
                lastOnlineTime,
                status,
                lastMatch
            };
            
            // 更新缓存
            userDataCache.set(user.steamId, {
                lastOnlineTime,
                status,
                lastMatch,
                timestamp: now
            });
            
            results.push(userData);
            completed++;
            
            console.log(`✅ 用户 ${user.name} 获取完成 - 状态: ${status}, 最后在线: ${lastOnlineTime ? lastOnlineTime.toLocaleString() : '未知'}`);
            
            // 更新进度
            if (onProgress) {
                onProgress(completed, users.length);
            }
            
            // 请求间隔，避免对服务器造成压力
            if (i < usersToFetch.length - 1) {
                console.log(`⏳ 等待 ${REQUEST_DELAY / 1000} 秒后处理下一个用户...`);
                await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY));
            }
            
        } catch (error) {
            console.error(`❌ 获取用户 ${user.name} (${user.steamId}) 数据失败:`, error);
            
            // 即使失败也添加到结果中，避免阻塞后续用户
            results.push({
                ...user,
                lastOnlineTime: null,
                status: 'unknown',
                lastMatch: null
            });
            
            completed++;
            if (onProgress) {
                onProgress(completed, users.length);
            }
            
            // 失败后也等待一段时间再继续
            if (i < usersToFetch.length - 1) {
                console.log(`⏳ 用户获取失败，等待 ${REQUEST_DELAY / 1000} 秒后继续...`);
                await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY));
            }
        }
    }
    
    console.log(`✅ 逐个获取完成，共处理 ${results.length} 个用户`);
    return results.sort((a, b) => a.name.localeCompare(b.name));
}

/**
 * 从比赛数据中提取最后在线时间
 * @param {Object|null} lastMatch - 最后一场比赛数据
 * @returns {Date|null} 最后在线时间
 */
function extractLastOnlineTime(lastMatch) {
    if (!lastMatch || !lastMatch.endTime) {
        return null;
    }
    
    try {
        return new Date(lastMatch.endTime);
    } catch (error) {
        console.error('❌ 解析在线时间失败:', error);
        return null;
    }
}

/**
 * 根据最后比赛时间判断用户状态
 * @param {Date|null} lastOnlineTime - 最后在线时间
 * @returns {string} 用户状态 ('online', 'recent', 'offline', 'unknown')
 */
function determineUserStatus(lastOnlineTime) {
    if (!lastOnlineTime) {
        return 'unknown';
    }
    
    const now = new Date();
    const diffHours = (now - lastOnlineTime) / (1000 * 60 * 60);
    const { RECENT_ACTIVE_HOURS } = window.USER_LIST_CONFIG;
    
    if (diffHours < 1) {
        return 'online';
    } else if (diffHours < RECENT_ACTIVE_HOURS) {
        return 'recent';
    } else {
        return 'offline';
    }
}

// 导出函数到全局作用域
window.UserListAPI = {
    getUserLastMatch,
    getUsersLastOnlineTime,
    extractLastMatchFromData,
    extractLastOnlineTime,
    determineUserStatus,
    userDataCache
};

console.log('✅ 用户列表 API 模块已加载（包含CORS代理支持）');