<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>战术档案分析</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .input-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .input-group {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .input-group label {
            font-weight: 600;
            color: #555;
            min-width: 120px;
        }

        .input-group input {
            flex: 1;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            min-width: 200px;
        }

        .input-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .profiles-section {
            display: none;
        }

        .profiles-section.show {
            display: block;
        }

        .player-profile {
            background: white;
            border-radius: 15px;
            margin-bottom: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .player-info h3 {
            font-size: 1.5em;
            margin-bottom: 5px;
        }

        .player-stats {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 1.8em;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .profile-content {
            padding: 25px;
        }

        .damage-analysis {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e1e5e9;
        }

        .unit-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .unit-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #667eea;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .unit-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .unit-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .unit-name {
            font-weight: 600;
            color: #333;
            font-size: 1.1em;
        }

        .unit-link {
            color: #667eea;
            text-decoration: none;
            font-size: 0.9em;
            font-weight: 500;
        }

        .unit-link:hover {
            text-decoration: underline;
        }

        .unit-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .unit-stat {
            text-align: center;
            padding: 8px;
            background: white;
            border-radius: 6px;
        }

        .unit-stat-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #667eea;
            display: block;
        }

        .unit-stat-label {
            font-size: 0.8em;
            color: #666;
            margin-top: 2px;
        }

        .category-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
            color: white;
        }

        .category-infantry { background: #28a745; }
        .category-vehicle { background: #dc3545; }
        .category-air { background: #17a2b8; }
        .category-navy { background: #20c997; }
        .category-support { background: #ffc107; color: #333; }
        .category-defense { background: #6f42c1; }
        .category-unknown { background: #6c757d; }

        .tactical-insights {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .insights-title {
            font-weight: 600;
            color: #1976d2;
            margin-bottom: 15px;
        }

        .insight-item {
            margin-bottom: 10px;
            padding-left: 20px;
            position: relative;
        }

        .insight-item::before {
            content: "•";
            color: #1976d2;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .match-history {
            margin-top: 20px;
        }

        .match-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
        }

        .match-item {
            padding: 12px 15px;
            border-bottom: 1px solid #e1e5e9;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .match-item:hover {
            background-color: #f8f9fa;
        }

        .match-item:last-child {
            border-bottom: none;
        }

        .match-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .match-id {
            font-weight: 600;
            color: #333;
        }

        .match-date {
            color: #666;
            font-size: 0.9em;
        }

        .match-damage {
            color: #667eea;
            font-weight: 500;
        }

        .filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-group label {
            font-weight: 500;
            color: #555;
        }

        .filter-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .input-group {
                flex-direction: column;
                align-items: stretch;
            }

            .input-group label {
                min-width: auto;
            }

            .player-stats {
                justify-content: center;
            }

            .unit-grid {
                grid-template-columns: 1fr;
            }

            .profile-header {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 战术档案分析</h1>
            <p>分析玩家的战术偏好和单位使用模式</p>
        </div>

        <div class="input-section">
            <div class="input-group">
                <label for="steamId">Steam ID:</label>
                <input type="text" id="steamId" placeholder="输入Steam ID (例如: 76561198039354882)" value="76561198039354882">
            </div>
            <div class="input-group">
                <label for="matchCount">分析场数:</label>
                <input type="number" id="matchCount" placeholder="分析最近的比赛场数" value="20" min="1" max="100">
            </div>
            <div class="input-group">
                <button class="btn" onclick="fetchPlayerData()">🔍 获取战术档案</button>
                <button class="btn" onclick="showHelp()">❓ 帮助</button>
            </div>
        </div>

        <div id="status" class="status" style="display: none;"></div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在分析玩家数据，请稍候...</p>
        </div>

        <div class="profiles-section" id="profilesSection">
            <div class="filters">
                <div class="filter-group">
                    <label>排序方式:</label>
                    <select id="sortBy" onchange="sortProfiles()">
                        <option value="totalDamage">总伤害</option>
                        <option value="matchCount">比赛场数</option>
                        <option value="avgDamage">平均伤害</option>
                        <option value="playerName">玩家名称</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>显示:</label>
                    <select id="filterBy" onchange="filterProfiles()">
                        <option value="all">所有玩家</option>
                        <option value="topDamage">伤害前10</option>
                        <option value="frequent">频繁玩家</option>
                    </select>
                </div>
            </div>
            <div id="profilesContainer"></div>
        </div>
    </div>

    <script src="js/unit-analysis.js"></script>
    <script src="js/unit-mapping.js"></script>
    <script>
        let playerProfiles = [];
        let allMatches = [];

        async function fetchPlayerData() {
            const steamId = document.getElementById('steamId').value.trim();
            const matchCount = parseInt(document.getElementById('matchCount').value) || 20;

            if (!steamId) {
                showStatus('请输入有效的Steam ID', 'error');
                return;
            }

            showLoading(true);
            showStatus('正在获取比赛数据...', 'info');

            try {
                const data = await fetchDataFromAPI(steamId);
                if (!data || !data.matches || data.matches.length === 0) {
                    showStatus('未找到比赛数据', 'error');
                    return;
                }

                allMatches = data.matches.slice(0, matchCount);
                showStatus(`成功获取 ${allMatches.length} 场比赛数据`, 'success');

                // 分析每个玩家的战术档案
                const profiles = analyzePlayerProfiles(allMatches);
                playerProfiles = profiles;

                displayProfiles(profiles);
                showProfilesSection();

            } catch (error) {
                console.error('获取数据失败:', error);
                showStatus(`获取数据失败: ${error.message}`, 'error');
            } finally {
                showLoading(false);
            }
        }

        async function fetchDataFromAPI(steamId) {
            const apiUrl = `https://v2202406227732275300.nicesrv.de:5000/statistic/getRecentMatches/${steamId}`;
            
            try {
                // 首先尝试直接请求
                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const text = await response.text();
                return parseMultiLineJSON(text);

            } catch (error) {
                console.log('直接请求失败，尝试通过代理:', error.message);
                
                // 通过本地代理重试
                const proxyUrl = `http://localhost:8080/proxy?url=${encodeURIComponent(apiUrl)}`;
                const proxyResponse = await fetch(proxyUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                if (!proxyResponse.ok) {
                    throw new Error(`代理请求失败: ${proxyResponse.status}`);
                }

                const text = await proxyResponse.text();
                return parseMultiLineJSON(text);
            }
        }

        function parseMultiLineJSON(rawText) {
            const lines = rawText.trim().split('\n');
            const matches = [];
            let metadata = null;

            for (const line of lines) {
                if (!line.trim()) continue;
                
                try {
                    const obj = JSON.parse(line);
                    if (obj.type === 'metadata') {
                        metadata = obj;
                    } else if (obj.type === 'match') {
                        matches.push(obj.data);
                    }
                } catch (e) {
                    console.warn('解析JSON行失败:', line.substring(0, 100));
                }
            }

            return { metadata, matches };
        }

        function analyzePlayerProfiles(matches) {
            const playerMap = new Map();

            matches.forEach(match => {
                if (!match.Data) return;

                Object.values(match.Data).forEach(playerData => {
                    const playerId = playerData.Id;
                    const playerName = playerData.Name;
                    const teamId = playerData.TeamId;

                    if (!playerMap.has(playerId)) {
                        playerMap.set(playerId, {
                            playerId: playerId,
                            playerName: playerName,
                            teamId: teamId,
                            matches: [],
                            totalDamage: 0,
                            totalKills: 0,
                            totalDamageReceived: 0,
                            unitUsage: new Map(),
                            categories: new Map(),
                            matchHistory: []
                        });
                    }

                    const profile = playerMap.get(playerId);
                    profile.matches.push(match);

                    // 分析单位使用
                    if (playerData.UnitData) {
                        let matchDamage = 0;
                        let matchKills = 0;
                        let matchDamageReceived = 0;

                        Object.values(playerData.UnitData).forEach(unitInfo => {
                            const unitId = unitInfo.Id;
                            const damage = unitInfo.TotalDamageDealt || 0;
                            const kills = unitInfo.KilledCount || 0;
                            const damageReceived = unitInfo.TotalDamageReceived || 0;
                            const options = unitInfo.OptionIds || [];

                            matchDamage += damage;
                            matchKills += kills;
                            matchDamageReceived += damageReceived;

                            // 更新单位使用统计
                            if (!profile.unitUsage.has(unitId)) {
                                profile.unitUsage.set(unitId, {
                                    unitId: unitId,
                                    unitName: getUnitName(unitId),
                                    category: getUnitCategory(unitId),
                                    totalDamage: 0,
                                    totalKills: 0,
                                    totalDamageReceived: 0,
                                    usageCount: 0,
                                    options: options
                                });
                            }

                            const unitStats = profile.unitUsage.get(unitId);
                            unitStats.totalDamage += damage;
                            unitStats.totalKills += kills;
                            unitStats.totalDamageReceived += damageReceived;
                            unitStats.usageCount++;
                            unitStats.options = options; // 更新最新的选项

                            // 更新分类统计
                            const category = unitStats.category;
                            if (!profile.categories.has(category)) {
                                profile.categories.set(category, {
                                    totalDamage: 0,
                                    totalKills: 0,
                                    usageCount: 0
                                });
                            }

                            const catStats = profile.categories.get(category);
                            catStats.totalDamage += damage;
                            catStats.totalKills += kills;
                            catStats.usageCount++;
                        });

                        profile.totalDamage += matchDamage;
                        profile.totalKills += matchKills;
                        profile.totalDamageReceived += matchDamageReceived;

                        // 记录比赛历史
                        profile.matchHistory.push({
                            matchId: match.fightId,
                            damage: matchDamage,
                            kills: matchKills,
                            damageReceived: matchDamageReceived,
                            date: new Date(match.EndTime * 1000)
                        });
                    }
                });
            });

            return Array.from(playerMap.values());
        }

        function displayProfiles(profiles) {
            const container = document.getElementById('profilesContainer');
            container.innerHTML = '';

            profiles.forEach(profile => {
                const profileElement = createProfileElement(profile);
                container.appendChild(profileElement);
            });
        }

        function createProfileElement(profile) {
            const div = document.createElement('div');
            div.className = 'player-profile';

            // 计算统计数据
            const avgDamage = profile.matches.length > 0 ? profile.totalDamage / profile.matches.length : 0;
            const avgKills = profile.matches.length > 0 ? profile.totalKills / profile.matches.length : 0;
            const efficiency = profile.totalDamageReceived > 0 ? profile.totalDamage / profile.totalDamageReceived : 0;

            // 获取主要伤害来源单位
            const topDamageUnits = Array.from(profile.unitUsage.values())
                .sort((a, b) => b.totalDamage - a.totalDamage)
                .slice(0, 5);

            // 获取主要分类
            const topCategories = Array.from(profile.categories.entries())
                .sort((a, b) => b[1].totalDamage - a[1].totalDamage)
                .slice(0, 3);

            div.innerHTML = `
                <div class="profile-header">
                    <div class="player-info">
                        <h3>${profile.playerName}</h3>
                        <p>ID: ${profile.playerId} | 队伍: ${profile.teamId}</p>
                    </div>
                    <div class="player-stats">
                        <div class="stat-item">
                            <span class="stat-value">${profile.matches.length}</span>
                            <span class="stat-label">比赛场数</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${profile.totalDamage.toFixed(0)}</span>
                            <span class="stat-label">总伤害</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${avgDamage.toFixed(0)}</span>
                            <span class="stat-label">平均伤害</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${efficiency.toFixed(2)}</span>
                            <span class="stat-label">伤害效率</span>
                        </div>
                    </div>
                </div>
                <div class="profile-content">
                    <div class="damage-analysis">
                        <h4 class="section-title">🎯 主要伤害来源单位</h4>
                        <div class="unit-grid">
                            ${topDamageUnits.map(unit => `
                                <div class="unit-card">
                                    <div class="unit-header">
                                        <span class="unit-name">${unit.unitName}</span>
                                        <a href="https://ba-hub.net/arsenal/${unit.unitId}${unit.options && unit.options.length > 0 ? `?m=${unit.options.join('-')}` : ''}" 
                                           target="_blank" class="unit-link">查看详情</a>
                                    </div>
                                    <div class="unit-stats">
                                        <div class="unit-stat">
                                            <span class="unit-stat-value">${unit.totalDamage.toFixed(0)}</span>
                                            <span class="unit-stat-label">总伤害</span>
                                        </div>
                                        <div class="unit-stat">
                                            <span class="unit-stat-value">${unit.usageCount}</span>
                                            <span class="unit-stat-label">使用次数</span>
                                        </div>
                                        <div class="unit-stat">
                                            <span class="unit-stat-value">${unit.totalKills}</span>
                                            <span class="unit-stat-label">击杀数</span>
                                        </div>
                                        <div class="unit-stat">
                                            <span class="unit-stat-value">${(unit.totalDamage / unit.usageCount).toFixed(0)}</span>
                                            <span class="unit-stat-label">平均伤害</span>
                                        </div>
                                    </div>
                                    <div style="margin-top: 10px;">
                                        <span class="category-badge category-${unit.category}">${unit.category}</span>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="tactical-insights">
                        <h4 class="insights-title">💡 战术洞察</h4>
                        <div class="insight-item">
                            主要依赖 ${topCategories[0] ? getCategoryName(topCategories[0][0]) : '未知'} 类型单位，占总伤害的 ${topCategories[0] ? ((topCategories[0][1].totalDamage / profile.totalDamage) * 100).toFixed(1) : 0}%
                        </div>
                        <div class="insight-item">
                            平均每场比赛造成 ${avgDamage.toFixed(0)} 伤害，击杀 ${avgKills.toFixed(1)} 个目标
                        </div>
                        <div class="insight-item">
                            伤害效率为 ${efficiency.toFixed(2)}，${efficiency > 1 ? '表现优秀' : '需要改进'}
                        </div>
                        <div class="insight-item">
                            最常使用的单位是 ${topDamageUnits[0]?.unitName || '未知'}，使用 ${topDamageUnits[0]?.usageCount || 0} 次
                        </div>
                    </div>

                    <div class="match-history">
                        <h4 class="section-title">📊 最近比赛记录</h4>
                        <div class="match-list">
                            ${profile.matchHistory.slice(-5).reverse().map(match => `
                                <div class="match-item">
                                    <div class="match-info">
                                        <span class="match-id">#${match.matchId}</span>
                                        <span class="match-date">${match.date.toLocaleDateString()}</span>
                                        <span class="match-damage">${match.damage.toFixed(0)} 伤害</span>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;

            return div;
        }

        function sortProfiles() {
            const sortBy = document.getElementById('sortBy').value;
            const sorted = [...playerProfiles].sort((a, b) => {
                switch (sortBy) {
                    case 'totalDamage':
                        return b.totalDamage - a.totalDamage;
                    case 'matchCount':
                        return b.matches.length - a.matches.length;
                    case 'avgDamage':
                        const avgA = a.matches.length > 0 ? a.totalDamage / a.matches.length : 0;
                        const avgB = b.matches.length > 0 ? b.totalDamage / b.matches.length : 0;
                        return avgB - avgA;
                    case 'playerName':
                        return a.playerName.localeCompare(b.playerName);
                    default:
                        return 0;
                }
            });
            displayProfiles(sorted);
        }

        function filterProfiles() {
            const filterBy = document.getElementById('filterBy').value;
            let filtered = [...playerProfiles];

            switch (filterBy) {
                case 'topDamage':
                    filtered = filtered.sort((a, b) => b.totalDamage - a.totalDamage).slice(0, 10);
                    break;
                case 'frequent':
                    filtered = filtered.filter(p => p.matches.length >= 3);
                    break;
                case 'all':
                default:
                    break;
            }

            displayProfiles(filtered);
        }

        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
        }

        function showLoading(show) {
            const loading = document.getElementById('loading');
            if (show) {
                loading.classList.add('show');
            } else {
                loading.classList.remove('show');
            }
        }

        function showProfilesSection() {
            document.getElementById('profilesSection').classList.add('show');
        }

        function showHelp() {
            showStatus(`
                使用说明：
                1. 输入Steam ID（例如：76561198039354882）
                2. 设置要分析的比赛场数（1-100）
                3. 点击"获取战术档案"开始分析
                4. 查看每个玩家的详细战术档案
                
                如果遇到网络问题，请确保本地代理服务器已启动：
                python cors-proxy.py
            `, 'info');
        }

        // 使用单位映射文件中的函数
        // getUnitName 和 getUnitCategory 函数已在 unit-mapping.js 中定义
    </script>
</body>
</html>
