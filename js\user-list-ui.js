/**
 * 用户列表 UI 渲染模块
 * 负责用户界面的渲染和交互
 */

// DOM 元素引用
let userListElements = {};

/**
 * 初始化 DOM 元素引用
 */
function initializeUserListDOMElements() {
    const ids = window.USER_LIST_DOM_IDS;
    
    userListElements = {
        refreshBtn: document.getElementById(ids.REFRESH_BTN),
        sortSelect: document.getElementById(ids.SORT_SELECT),
        sortOrderBtn: document.getElementById(ids.SORT_ORDER_BTN),
        filterInput: document.getElementById(ids.FILTER_INPUT),
        statusFilter: document.getElementById(ids.STATUS_FILTER),
        loading: document.getElementById(ids.LOADING),
        error: document.getElementById(ids.ERROR),
        userStats: document.getElementById(ids.USER_STATS),
        totalUsers: document.getElementById(ids.TOTAL_USERS),
        onlineUsers: document.getElementById(ids.ONLINE_USERS),
        recentUsers: document.getElementById(ids.RECENT_USERS),
        offlineUsers: document.getElementById(ids.OFFLINE_USERS),
        userListContainer: document.getElementById(ids.USER_LIST_CONTAINER),
        userList: document.getElementById(ids.USER_LIST),
        progressContainer: document.getElementById(ids.PROGRESS_CONTAINER),
        progressFill: document.getElementById(ids.PROGRESS_FILL),
        progressText: document.getElementById(ids.PROGRESS_TEXT)
    };
    
    console.log('✅ 用户列表 DOM 元素已初始化');
}

/**
 * 显示错误信息
 * @param {string} message - 错误消息
 */
function showUserListError(message) {
    if (userListElements.error) {
        userListElements.error.textContent = message;
        userListElements.error.classList.add('show');
    }
}

/**
 * 显示加载状态
 */
function showUserListLoading() {
    if (userListElements.loading) {
        userListElements.loading.classList.add('show');
    }
    if (userListElements.error) {
        userListElements.error.classList.remove('show');
    }
}

/**
 * 隐藏加载状态
 */
function hideUserListLoading() {
    if (userListElements.loading) {
        userListElements.loading.classList.remove('show');
    }
    if (userListElements.progressContainer) {
        userListElements.progressContainer.style.display = 'none';
    }
}

/**
 * 更新用户列表进度
 * @param {number} current - 当前进度
 * @param {number} total - 总数
 */
function updateUserListProgress(current, total) {
    const progressContainer = document.getElementById(window.USER_LIST_DOM_IDS.PROGRESS_CONTAINER);
    const progressFill = document.getElementById(window.USER_LIST_DOM_IDS.PROGRESS_FILL);
    const progressText = document.getElementById(window.USER_LIST_DOM_IDS.PROGRESS_TEXT);
    
    if (progressContainer && progressFill && progressText) {
        const percentage = Math.round((current / total) * 100);
        progressFill.style.width = `${percentage}%`;
        progressText.textContent = `正在逐个获取用户数据... ${current}/${total} (${percentage}%)`;
        progressContainer.style.display = 'block';
    }
}

/**
 * 渲染用户统计
 * @param {Array} users - 用户列表
 */
function renderUserStats(users) {
    const stats = {
        total: users.length,
        online: users.filter(u => u.status === 'online').length,
        recent: users.filter(u => u.status === 'recent').length,
        offline: users.filter(u => u.status === 'offline').length
    };
    
    if (userListElements.totalUsers) userListElements.totalUsers.textContent = stats.total;
    if (userListElements.onlineUsers) userListElements.onlineUsers.textContent = stats.online;
    if (userListElements.recentUsers) userListElements.recentUsers.textContent = stats.recent;
    if (userListElements.offlineUsers) userListElements.offlineUsers.textContent = stats.offline;
    
    if (userListElements.userStats) {
        userListElements.userStats.style.display = 'flex';
    }
}

/**
 * 渲染用户列表
 * @param {Array} users - 用户列表
 * @param {string} filterText - 过滤文本
 * @param {string} statusFilter - 状态过滤
 */
function renderUserList(users, filterText = '', statusFilter = 'all') {
    if (!userListElements.userList) return;
    
    // 过滤用户
    let filteredUsers = users;
    
    if (filterText) {
        filteredUsers = filteredUsers.filter(user => 
            user.name.toLowerCase().includes(filterText.toLowerCase())
        );
    }
    
    if (statusFilter !== 'all') {
        filteredUsers = filteredUsers.filter(user => user.status === statusFilter);
    }
    
    // 生成HTML
    const html = filteredUsers.map(user => createUserItemHtml(user)).join('');
    userListElements.userList.innerHTML = html;
    
    // 绑定点击事件
    bindUserItemEvents();
    
    // 显示容器
    if (userListElements.userListContainer) {
        userListElements.userListContainer.style.display = 'block';
    }
    
    console.log(`✅ 渲染了 ${filteredUsers.length}/${users.length} 个用户`);
}

/**
 * 创建单个用户项的HTML
 * @param {Object} user - 用户数据
 * @returns {string} HTML字符串
 */
function createUserItemHtml(user) {
    const avatar = user.name.charAt(0).toUpperCase();
    const lastOnlineText = formatLastOnlineTime(user.lastOnlineTime);
    const statusInfo = getStatusInfo(user.status);
    
    return `
        <div class="user-item" data-steam-id="${escapeHtml(user.steamId)}" data-user-name="${escapeHtml(user.name)}">
            <div class="user-avatar">${escapeHtml(avatar)}</div>
            <div class="user-info">
                <div class="user-name" title="${escapeHtml(user.name)}">${escapeHtml(user.name)}</div>
                <div class="user-steam-id">${escapeHtml(user.steamId)}</div>
                <div class="user-last-online">${lastOnlineText}</div>
            </div>
            <div class="user-status">
                <div class="status-indicator ${statusInfo.class}"></div>
                <div class="status-text ${statusInfo.class}">${statusInfo.text}</div>
            </div>
        </div>
    `;
}

/**
 * 格式化最后在线时间
 * @param {Date|null} lastOnlineTime - 最后在线时间
 * @returns {string} 格式化的时间字符串
 */
function formatLastOnlineTime(lastOnlineTime) {
    if (!lastOnlineTime) {
        return '未知';
    }
    
    const now = new Date();
    const diff = now - lastOnlineTime;
    const { RELATIVE_TIME_THRESHOLDS } = window.USER_LIST_TIME_CONFIG;
    
    if (diff < RELATIVE_TIME_THRESHOLDS.MINUTE) {
        return '刚刚';
    } else if (diff < RELATIVE_TIME_THRESHOLDS.HOUR) {
        const minutes = Math.floor(diff / RELATIVE_TIME_THRESHOLDS.MINUTE);
        return `${minutes}分钟前`;
    } else if (diff < RELATIVE_TIME_THRESHOLDS.DAY) {
        const hours = Math.floor(diff / RELATIVE_TIME_THRESHOLDS.HOUR);
        return `${hours}小时前`;
    } else if (diff < RELATIVE_TIME_THRESHOLDS.WEEK) {
        const days = Math.floor(diff / RELATIVE_TIME_THRESHOLDS.DAY);
        return `${days}天前`;
    } else {
        return lastOnlineTime.toLocaleDateString('zh-CN', window.USER_LIST_TIME_CONFIG.DATE_FORMAT);
    }
}

/**
 * 获取状态信息
 * @param {string} status - 用户状态
 * @returns {Object} 状态信息对象
 */
function getStatusInfo(status) {
    switch (status) {
        case 'online':
            return { class: 'online', text: '在线' };
        case 'recent':
            return { class: 'recent', text: '最近' };
        case 'offline':
            return { class: 'offline', text: '离线' };
        default:
            return { class: 'offline', text: '未知' };
    }
}

/**
 * 转义HTML字符
 * @param {string} text - 要转义的文本
 * @returns {string} 转义后的文本
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * 绑定用户项点击事件
 */
function bindUserItemEvents() {
    const userItems = document.querySelectorAll('.user-item');
    userItems.forEach(item => {
        item.addEventListener('click', () => {
            const steamId = item.dataset.steamId;
            const userName = item.dataset.userName;
            
            console.log(`🔗 点击用户: ${userName} (${steamId})`);
            
            // 跳转到比赛记录查询页面
            const url = `index.html?steamId=${encodeURIComponent(steamId)}&name=${encodeURIComponent(userName)}`;
            window.location.href = url;
        });
    });
}

/**
 * 排序用户列表
 * @param {Array} users - 用户列表
 * @param {string} sortBy - 排序字段
 * @param {boolean} ascending - 是否升序
 * @returns {Array} 排序后的用户列表
 */
function sortUsers(users, sortBy, ascending = true) {
    const sorted = [...users].sort((a, b) => {
        let valueA, valueB;
        
        switch (sortBy) {
            case 'name':
                valueA = a.name.toLowerCase();
                valueB = b.name.toLowerCase();
                break;
            case 'lastOnline':
                valueA = a.lastOnlineTime ? a.lastOnlineTime.getTime() : 0;
                valueB = b.lastOnlineTime ? b.lastOnlineTime.getTime() : 0;
                break;
            case 'status':
                const statusOrder = { 'online': 3, 'recent': 2, 'offline': 1, 'unknown': 0 };
                valueA = statusOrder[a.status] || 0;
                valueB = statusOrder[b.status] || 0;
                break;
            default:
                return 0;
        }
        
        if (valueA < valueB) return ascending ? -1 : 1;
        if (valueA > valueB) return ascending ? 1 : -1;
        return 0;
    });
    
    return sorted;
}

/**
 * 更新排序按钮状态
 * @param {boolean} ascending - 是否升序
 */
function updateSortOrderButton(ascending) {
    if (userListElements.sortOrderBtn) {
        userListElements.sortOrderBtn.textContent = ascending ? '⬇️' : '⬆️';
        userListElements.sortOrderBtn.title = ascending ? '点击切换为降序' : '点击切换为升序';
    }
}

/**
 * 设置刷新按钮状态
 * @param {boolean} loading - 是否正在加载
 */
function setRefreshButtonState(loading) {
    if (userListElements.refreshBtn) {
        userListElements.refreshBtn.disabled = loading;
        if (loading) {
            userListElements.refreshBtn.classList.add('loading');
        } else {
            userListElements.refreshBtn.classList.remove('loading');
        }
    }
}

// 导出函数到全局作用域
window.UserListUI = {
    initializeUserListDOMElements,
    showUserListError,
    showUserListLoading,
    hideUserListLoading,
    updateUserListProgress,
    renderUserStats,
    renderUserList,
    createUserItemHtml,
    formatLastOnlineTime,
    getStatusInfo,
    escapeHtml,
    bindUserItemEvents,
    sortUsers,
    updateSortOrderButton,
    setRefreshButtonState
};

console.log('✅ 用户列表 UI 模块已加载');