/**
 * 增强数据分析模块
 * 从比赛数据中提取更多有用信息
 */

console.log('🔍 增强数据分析模块已加载！');

/**
 * 从比赛数据中提取详细信息
 * @param {Object} matchData - 比赛数据
 * @returns {Object} 增强的分析结果
 */
function analyzeMatchData(matchData) {
    const analysis = {
        // 基础信息
        basic: extractBasicInfo(matchData),
        // 玩家统计
        players: extractPlayerStats(matchData),
        // 单位使用统计
        units: extractUnitUsageStats(matchData),
        // 游戏分析
        gameplay: extractGameplayAnalysis(matchData),
        // 性能指标
        performance: extractPerformanceMetrics(matchData),
        // 时间分析
        timing: extractTimingAnalysis(matchData),
        // 地图分析
        map: extractMapAnalysis(matchData),
        // 趋势分析
        trends: extractTrendAnalysis(matchData)
    };
    
    return analysis;
}

/**
 * 提取基础信息
 */
function extractBasicInfo(matchData) {
    const data = matchData.data || matchData;
    
    return {
        matchId: data.fightId || data.match_id || data.id,
        endTime: data.EndTime || data.date || data.endTime,
        mapId: data.MapId || data.map_id,
        mapName: getMapName(data.MapId || data.map_id),
        duration: data.TotalPlayTimeInSec || data.duration || 0,
        ratingChange: data.ratingChange || data.elo || 0,
        result: (data.ratingChange || data.elo || 0) > 0 ? 'win' : 'loss',
        playerCount: data.Data ? Object.keys(data.Data).length : 0
    };
}

/**
 * 提取玩家统计信息
 */
function extractPlayerStats(matchData) {
    const data = matchData.data || matchData;
    const players = data.Data || {};
    
    const playerStats = {
        totalPlayers: Object.keys(players).length,
        playerDetails: [],
        damageStats: {
            totalDamage: 0,
            averageDamage: 0,
            maxDamage: 0,
            minDamage: Infinity,
            damageDistribution: {
                low: 0,      // 0-500
                medium: 0,   // 500-1500
                high: 0,     // 1500-2500
                extreme: 0   // 2500+
            }
        },
        killStats: {
            totalKills: 0,
            averageKills: 0,
            maxKills: 0,
            minKills: Infinity
        },
        deathStats: {
            totalDeaths: 0,
            averageDeaths: 0,
            maxDeaths: 0,
            minDeaths: Infinity
        }
    };
    
    // 分析每个玩家
    for (const [playerId, playerData] of Object.entries(players)) {
        const damage = playerData.DamageDealt || 0;
        const kills = playerData.Kills || 0;
        const deaths = playerData.Deaths || 0;
        
        // 伤害分布统计
        if (damage <= 500) playerStats.damageStats.damageDistribution.low++;
        else if (damage <= 1500) playerStats.damageStats.damageDistribution.medium++;
        else if (damage <= 2500) playerStats.damageStats.damageDistribution.high++;
        else playerStats.damageStats.damageDistribution.extreme++;
        
        // 累计统计
        playerStats.damageStats.totalDamage += damage;
        playerStats.killStats.totalKills += kills;
        playerStats.deathStats.totalDeaths += deaths;
        
        // 最大最小值
        if (damage > playerStats.damageStats.maxDamage) playerStats.damageStats.maxDamage = damage;
        if (damage < playerStats.damageStats.minDamage) playerStats.damageStats.minDamage = damage;
        if (kills > playerStats.killStats.maxKills) playerStats.killStats.maxKills = kills;
        if (kills < playerStats.killStats.minKills) playerStats.killStats.minKills = kills;
        if (deaths > playerStats.deathStats.maxDeaths) playerStats.deathStats.maxDeaths = deaths;
        if (deaths < playerStats.deathStats.minDeaths) playerStats.deathStats.minDeaths = deaths;
        
        // 玩家详情
        playerStats.playerDetails.push({
            playerId: playerId,
            damage: damage,
            kills: kills,
            deaths: deaths,
            kdRatio: deaths > 0 ? (kills / deaths).toFixed(2) : kills,
            damagePerKill: kills > 0 ? (damage / kills).toFixed(0) : 0,
            performance: calculatePlayerPerformance(damage, kills, deaths)
        });
    }
    
    // 计算平均值
    if (playerStats.totalPlayers > 0) {
        playerStats.damageStats.averageDamage = Math.round(playerStats.damageStats.totalDamage / playerStats.totalPlayers);
        playerStats.killStats.averageKills = (playerStats.killStats.totalKills / playerStats.totalPlayers).toFixed(1);
        playerStats.deathStats.averageDeaths = (playerStats.deathStats.totalDeaths / playerStats.totalPlayers).toFixed(1);
    }
    
    // 处理最小值为Infinity的情况
    if (playerStats.damageStats.minDamage === Infinity) playerStats.damageStats.minDamage = 0;
    if (playerStats.killStats.minKills === Infinity) playerStats.killStats.minKills = 0;
    if (playerStats.deathStats.minDeaths === Infinity) playerStats.deathStats.minDeaths = 0;
    
    return playerStats;
}

/**
 * 提取单位使用统计信息
 */
function extractUnitUsageStats(matchData) {
    const data = matchData.data || matchData;
    const players = data.Data || {};
    
    const unitStats = {
        totalUnits: 0,
        uniqueUnits: new Set(),
        unitUsage: {},
        playerUnitDetails: [],
        unitCategories: {
            infantry: 0,
            vehicle: 0,
            aircraft: 0,
            artillery: 0,
            support: 0,
            unknown: 0
        },
        mostUsedUnits: [],
        unitEfficiency: {}
    };
    
    // 分析每个玩家的单位使用情况
    for (const [playerId, playerData] of Object.entries(players)) {
        const unitData = playerData.UnitData || {};
        const transferredUnits = playerData.TransferredUnits || {};
        
        const playerUnitInfo = {
            playerId: playerId,
            playerName: playerData.Name || playerId,
            units: [],
            totalUnits: 0,
            unitTypes: new Set(),
            unitEfficiency: {}
        };
        
        // 分析主要单位数据
        for (const [unitId, unitInfo] of Object.entries(unitData)) {
            const unitId_num = parseInt(unitInfo.Id);
            const unitName = getUnitName(unitId_num);
            const unitCategory = categorizeUnit(unitId_num);
            
            // 统计单位使用
            if (!unitStats.unitUsage[unitId_num]) {
                unitStats.unitUsage[unitId_num] = {
                    id: unitId_num,
                    name: unitName,
                    category: unitCategory,
                    usageCount: 0,
                    totalDamage: 0,
                    totalKills: 0,
                    totalDeaths: 0,
                    players: []
                };
            }
            
            unitStats.unitUsage[unitId_num].usageCount++;
            unitStats.unitUsage[unitId_num].totalDamage += unitInfo.TotalDamageDealt || 0;
            unitStats.unitUsage[unitId_num].totalKills += unitInfo.KilledCount || 0;
            unitStats.unitUsage[unitId_num].players.push(playerId);
            
            // 更新分类统计
            unitStats.unitCategories[unitCategory]++;
            unitStats.uniqueUnits.add(unitId_num);
            unitStats.totalUnits++;
            
            // 玩家单位详情
            playerUnitInfo.units.push({
                unitId: unitId_num,
                unitName: unitName,
                category: unitCategory,
                damage: unitInfo.TotalDamageDealt || 0,
                kills: unitInfo.KilledCount || 0,
                deaths: unitInfo.TotalDamageReceived || 0,
                wasRefunded: unitInfo.WasRefunded || false,
                options: unitInfo.OptionIds || []
            });
            
            playerUnitInfo.totalUnits++;
            playerUnitInfo.unitTypes.add(unitCategory);
        }
        
        // 分析转移的单位
        for (const [unitId, unitInfo] of Object.entries(transferredUnits)) {
            const unitId_num = parseInt(unitInfo.Id);
            const unitName = getUnitName(unitId_num);
            const unitCategory = categorizeUnit(unitId_num);
            
            playerUnitInfo.units.push({
                unitId: unitId_num,
                unitName: unitName,
                category: unitCategory,
                damage: 0,
                kills: 0,
                deaths: 0,
                wasRefunded: false,
                options: unitInfo.OptionIds || [],
                transferred: true
            });
            
            playerUnitInfo.totalUnits++;
            playerUnitInfo.unitTypes.add(unitCategory);
        }
        
        // 计算玩家单位效率
        playerUnitInfo.unitEfficiency = calculatePlayerUnitEfficiency(playerUnitInfo.units);
        
        unitStats.playerUnitDetails.push(playerUnitInfo);
    }
    
    // 生成最常用单位列表
    unitStats.mostUsedUnits = Object.values(unitStats.unitUsage)
        .sort((a, b) => b.usageCount - a.usageCount)
        .slice(0, 10);
    
    // 计算单位效率统计
    unitStats.unitEfficiency = calculateOverallUnitEfficiency(unitStats.unitUsage);
    
    return unitStats;
}

/**
 * 获取单位名称
 */
function getUnitName(unitId) {
    // 这里可以添加单位ID到名称的映射
    // 暂时返回单位ID
    return `单位${unitId}`;
}

/**
 * 单位分类
 */
function categorizeUnit(unitId) {
    // 基于单位ID进行分类
    // 这些分类需要根据实际游戏单位进行调整
    if (unitId >= 1 && unitId <= 50) return 'infantry';      // 步兵
    if (unitId >= 51 && unitId <= 150) return 'vehicle';     // 车辆
    if (unitId >= 151 && unitId <= 250) return 'aircraft';   // 飞机
    if (unitId >= 251 && unitId <= 350) return 'artillery';  // 炮兵
    if (unitId >= 351 && unitId <= 450) return 'support';    // 支援
    return 'unknown';
}

/**
 * 计算玩家单位效率
 */
function calculatePlayerUnitEfficiency(units) {
    const totalDamage = units.reduce((sum, unit) => sum + unit.damage, 0);
    const totalKills = units.reduce((sum, unit) => sum + unit.kills, 0);
    const totalDeaths = units.reduce((sum, unit) => sum + unit.deaths, 0);
    const activeUnits = units.filter(unit => !unit.wasRefunded && !unit.transferred);
    
    return {
        totalDamage: totalDamage,
        totalKills: totalKills,
        totalDeaths: totalDeaths,
        activeUnits: activeUnits.length,
        damagePerUnit: activeUnits.length > 0 ? (totalDamage / activeUnits.length).toFixed(1) : 0,
        killsPerUnit: activeUnits.length > 0 ? (totalKills / activeUnits.length).toFixed(1) : 0,
        efficiency: activeUnits.length > 0 ? ((totalDamage + totalKills * 100) / activeUnits.length).toFixed(0) : 0
    };
}

/**
 * 计算整体单位效率
 */
function calculateOverallUnitEfficiency(unitUsage) {
    const efficiency = {
        mostEfficient: [],
        leastEfficient: [],
        averageEfficiency: 0
    };
    
    const unitEfficiencies = Object.values(unitUsage).map(unit => ({
        id: unit.id,
        name: unit.name,
        efficiency: unit.usageCount > 0 ? (unit.totalDamage + unit.totalKills * 100) / unit.usageCount : 0
    }));
    
    // 按效率排序
    unitEfficiencies.sort((a, b) => b.efficiency - a.efficiency);
    
    efficiency.mostEfficient = unitEfficiencies.slice(0, 5);
    efficiency.leastEfficient = unitEfficiencies.slice(-5).reverse();
    efficiency.averageEfficiency = unitEfficiencies.reduce((sum, unit) => sum + unit.efficiency, 0) / unitEfficiencies.length;
    
    return efficiency;
}

/**
 * 提取游戏玩法分析
 */
function extractGameplayAnalysis(matchData) {
    const data = matchData.data || matchData;
    const players = data.Data || {};
    
    const gameplay = {
        intensity: calculateGameIntensity(players),
        balance: calculateGameBalance(players),
        skillGap: calculateSkillGap(players),
        engagement: calculateEngagementLevel(players)
    };
    
    return gameplay;
}

/**
 * 提取性能指标
 */
function extractPerformanceMetrics(matchData) {
    const data = matchData.data || matchData;
    const players = data.Data || {};
    
    const metrics = {
        efficiency: calculateEfficiencyMetrics(players),
        consistency: calculateConsistencyMetrics(players),
        dominance: calculateDominanceMetrics(players)
    };
    
    return metrics;
}

/**
 * 提取时间分析
 */
function extractTimingAnalysis(matchData) {
    const data = matchData.data || matchData;
    const endTime = data.EndTime || data.date || data.endTime;
    const duration = data.TotalPlayTimeInSec || data.duration || 0;
    
    const timing = {
        endTime: endTime,
        duration: duration,
        durationFormatted: formatDuration(duration),
        timeOfDay: extractTimeOfDay(endTime),
        dayOfWeek: extractDayOfWeek(endTime),
        isLongGame: duration > 600, // 超过10分钟
        isShortGame: duration < 300, // 少于5分钟
        gameSpeed: calculateGameSpeed(duration, data.Data || {})
    };
    
    return timing;
}

/**
 * 提取地图分析
 */
function extractMapAnalysis(matchData) {
    const data = matchData.data || matchData;
    const mapId = data.MapId || data.map_id;
    
    const map = {
        mapId: mapId,
        mapName: getMapName(mapId),
        mapType: categorizeMap(mapId),
        mapPopularity: getMapPopularity(mapId)
    };
    
    return map;
}

/**
 * 提取趋势分析
 */
function extractTrendAnalysis(matchData) {
    // 这里可以基于历史数据进行分析
    // 暂时返回基础信息
    return {
        ratingTrend: 'stable', // 需要历史数据
        performanceTrend: 'stable', // 需要历史数据
        mapPreference: 'unknown' // 需要历史数据
    };
}

// 辅助函数

/**
 * 计算玩家表现评分
 */
function calculatePlayerPerformance(damage, kills, deaths) {
    let score = 0;
    
    // 伤害评分 (0-40分)
    if (damage >= 2500) score += 40;
    else if (damage >= 2000) score += 35;
    else if (damage >= 1500) score += 30;
    else if (damage >= 1000) score += 25;
    else if (damage >= 500) score += 20;
    else score += 10;
    
    // K/D评分 (0-40分)
    if (deaths === 0) {
        score += kills * 8; // 无死亡，每杀8分
    } else {
        const kdRatio = kills / deaths;
        if (kdRatio >= 3) score += 40;
        else if (kdRatio >= 2) score += 35;
        else if (kdRatio >= 1.5) score += 30;
        else if (kdRatio >= 1) score += 25;
        else if (kdRatio >= 0.5) score += 15;
        else score += 5;
    }
    
    // 效率评分 (0-20分)
    const damagePerKill = kills > 0 ? damage / kills : 0;
    if (damagePerKill >= 1000) score += 20;
    else if (damagePerKill >= 800) score += 15;
    else if (damagePerKill >= 600) score += 10;
    else if (damagePerKill >= 400) score += 5;
    
    return Math.min(100, score);
}

/**
 * 计算游戏强度
 */
function calculateGameIntensity(players) {
    const totalDamage = Object.values(players).reduce((sum, player) => sum + (player.DamageDealt || 0), 0);
    const totalKills = Object.values(players).reduce((sum, player) => sum + (player.Kills || 0), 0);
    const playerCount = Object.keys(players).length;
    
    if (playerCount === 0) return 'unknown';
    
    const avgDamage = totalDamage / playerCount;
    const avgKills = totalKills / playerCount;
    
    if (avgDamage >= 2000 && avgKills >= 5) return 'extreme';
    if (avgDamage >= 1500 && avgKills >= 3) return 'high';
    if (avgDamage >= 1000 && avgKills >= 2) return 'medium';
    if (avgDamage >= 500 && avgKills >= 1) return 'low';
    return 'very_low';
}

/**
 * 计算游戏平衡性
 */
function calculateGameBalance(players) {
    const damages = Object.values(players).map(player => player.DamageDealt || 0).sort((a, b) => b - a);
    if (damages.length < 2) return 'unknown';
    
    const maxDamage = damages[0];
    const minDamage = damages[damages.length - 1];
    const avgDamage = damages.reduce((sum, d) => sum + d, 0) / damages.length;
    
    const variance = damages.reduce((sum, d) => sum + Math.pow(d - avgDamage, 2), 0) / damages.length;
    const stdDev = Math.sqrt(variance);
    const coefficientOfVariation = stdDev / avgDamage;
    
    if (coefficientOfVariation <= 0.3) return 'very_balanced';
    if (coefficientOfVariation <= 0.5) return 'balanced';
    if (coefficientOfVariation <= 0.7) return 'unbalanced';
    return 'very_unbalanced';
}

/**
 * 计算技能差距
 */
function calculateSkillGap(players) {
    const damages = Object.values(players).map(player => player.DamageDealt || 0).sort((a, b) => b - a);
    if (damages.length < 2) return 'unknown';
    
    const maxDamage = damages[0];
    const minDamage = damages[damages.length - 1];
    const gap = maxDamage - minDamage;
    const avgDamage = damages.reduce((sum, d) => sum + d, 0) / damages.length;
    
    const gapRatio = gap / avgDamage;
    
    if (gapRatio <= 1) return 'low';
    if (gapRatio <= 2) return 'medium';
    if (gapRatio <= 3) return 'high';
    return 'extreme';
}

/**
 * 计算参与度
 */
function calculateEngagementLevel(players) {
    const activePlayers = Object.values(players).filter(player => 
        (player.DamageDealt || 0) > 100 || (player.Kills || 0) > 0
    ).length;
    const totalPlayers = Object.keys(players).length;
    
    if (totalPlayers === 0) return 'unknown';
    
    const engagementRate = activePlayers / totalPlayers;
    
    if (engagementRate >= 0.9) return 'very_high';
    if (engagementRate >= 0.8) return 'high';
    if (engagementRate >= 0.7) return 'medium';
    if (engagementRate >= 0.6) return 'low';
    return 'very_low';
}

/**
 * 计算效率指标
 */
function calculateEfficiencyMetrics(players) {
    const efficiencies = Object.values(players).map(player => {
        const damage = player.DamageDealt || 0;
        const kills = player.Kills || 0;
        const deaths = player.Deaths || 0;
        
        return {
            damagePerMinute: damage / Math.max(1, (player.PlayTimeInSec || 60) / 60),
            killsPerMinute: kills / Math.max(1, (player.PlayTimeInSec || 60) / 60),
            damagePerKill: kills > 0 ? damage / kills : 0
        };
    });
    
    const avgDamagePerMinute = efficiencies.reduce((sum, e) => sum + e.damagePerMinute, 0) / efficiencies.length;
    const avgKillsPerMinute = efficiencies.reduce((sum, e) => sum + e.killsPerMinute, 0) / efficiencies.length;
    const avgDamagePerKill = efficiencies.reduce((sum, e) => sum + e.damagePerKill, 0) / efficiencies.length;
    
    return {
        avgDamagePerMinute: Math.round(avgDamagePerMinute),
        avgKillsPerMinute: avgKillsPerMinute.toFixed(2),
        avgDamagePerKill: Math.round(avgDamagePerKill)
    };
}

/**
 * 计算一致性指标
 */
function calculateConsistencyMetrics(players) {
    const damages = Object.values(players).map(player => player.DamageDealt || 0);
    const avgDamage = damages.reduce((sum, d) => sum + d, 0) / damages.length;
    
    const variance = damages.reduce((sum, d) => sum + Math.pow(d - avgDamage, 2), 0) / damages.length;
    const stdDev = Math.sqrt(variance);
    const coefficientOfVariation = stdDev / avgDamage;
    
    return {
        consistency: coefficientOfVariation <= 0.5 ? 'high' : 'low',
        variance: Math.round(variance),
        stdDev: Math.round(stdDev)
    };
}

/**
 * 计算统治力指标
 */
function calculateDominanceMetrics(players) {
    const damages = Object.values(players).map(player => player.DamageDealt || 0).sort((a, b) => b - a);
    if (damages.length === 0) return { topPlayerShare: 0, dominanceLevel: 'none' };
    
    const totalDamage = damages.reduce((sum, d) => sum + d, 0);
    const topPlayerDamage = damages[0];
    const topPlayerShare = totalDamage > 0 ? (topPlayerDamage / totalDamage) * 100 : 0;
    
    let dominanceLevel = 'none';
    if (topPlayerShare >= 40) dominanceLevel = 'extreme';
    else if (topPlayerShare >= 30) dominanceLevel = 'high';
    else if (topPlayerShare >= 25) dominanceLevel = 'medium';
    else if (topPlayerShare >= 20) dominanceLevel = 'low';
    
    return {
        topPlayerShare: Math.round(topPlayerShare),
        dominanceLevel: dominanceLevel
    };
}

/**
 * 提取一天中的时间
 */
function extractTimeOfDay(timestamp) {
    if (!timestamp) return 'unknown';
    
    const date = new Date(typeof timestamp === 'number' ? timestamp * 1000 : timestamp);
    const hour = date.getHours();
    
    if (hour >= 6 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 18) return 'afternoon';
    if (hour >= 18 && hour < 24) return 'evening';
    return 'night';
}

/**
 * 提取星期几
 */
function extractDayOfWeek(timestamp) {
    if (!timestamp) return 'unknown';
    
    const date = new Date(typeof timestamp === 'number' ? timestamp * 1000 : timestamp);
    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    return days[date.getDay()];
}

/**
 * 计算游戏速度
 */
function calculateGameSpeed(duration, players) {
    if (duration === 0) return 'unknown';
    
    const totalKills = Object.values(players).reduce((sum, player) => sum + (player.Kills || 0), 0);
    const killsPerMinute = totalKills / (duration / 60);
    
    if (killsPerMinute >= 2) return 'very_fast';
    if (killsPerMinute >= 1.5) return 'fast';
    if (killsPerMinute >= 1) return 'normal';
    if (killsPerMinute >= 0.5) return 'slow';
    return 'very_slow';
}

/**
 * 地图分类
 */
function categorizeMap(mapId) {
    // 基于地图ID进行分类
    const smallMaps = [1, 2, 3, 4, 5]; // 小地图
    const mediumMaps = [6, 7, 8, 9, 10, 11, 12, 13, 14, 15]; // 中等地图
    const largeMaps = [16, 17, 18, 19, 23]; // 大地图
    
    if (smallMaps.includes(mapId)) return 'small';
    if (mediumMaps.includes(mapId)) return 'medium';
    if (largeMaps.includes(mapId)) return 'large';
    return 'unknown';
}

/**
 * 获取地图受欢迎程度
 */
function getMapPopularity(mapId) {
    // 这里可以基于历史数据统计
    // 暂时返回默认值
    return 'unknown';
}

/**
 * 格式化分析结果为可读文本
 */
function formatAnalysisResult(analysis) {
    const result = {
        summary: generateSummary(analysis),
        details: generateDetails(analysis),
        insights: generateInsights(analysis),
        unitInsights: generateUnitInsights(analysis)
    };
    
    return result;
}

/**
 * 生成摘要
 */
function generateSummary(analysis) {
    const basic = analysis.basic;
    const players = analysis.players;
    const gameplay = analysis.gameplay;
    const units = analysis.units;
    
    return {
        matchInfo: `${basic.mapName} - ${basic.result.toUpperCase()} (${basic.durationFormatted})`,
        playerCount: `${players.totalPlayers} 玩家`,
        intensity: `游戏强度: ${gameplay.intensity}`,
        balance: `平衡性: ${gameplay.balance}`,
        topDamage: `最高伤害: ${players.damageStats.maxDamage}`,
        avgDamage: `平均伤害: ${players.damageStats.averageDamage}`,
        unitInfo: `单位使用: ${units.totalUnits} 个, ${units.uniqueUnits.size} 种`
    };
}

/**
 * 生成详细信息
 */
function generateDetails(analysis) {
    const players = analysis.players;
    const performance = analysis.performance;
    const timing = analysis.timing;
    const units = analysis.units;
    
    return {
        damageBreakdown: {
            total: players.damageStats.totalDamage,
            average: players.damageStats.averageDamage,
            distribution: players.damageStats.damageDistribution
        },
        killStats: {
            total: players.killStats.totalKills,
            average: players.killStats.averageKills,
            max: players.killStats.maxKills
        },
        efficiency: {
            damagePerMinute: performance.efficiency.avgDamagePerMinute,
            killsPerMinute: performance.efficiency.avgKillsPerMinute,
            damagePerKill: performance.efficiency.avgDamagePerKill
        },
        timing: {
            duration: timing.durationFormatted,
            timeOfDay: timing.timeOfDay,
            dayOfWeek: timing.dayOfWeek,
            gameSpeed: timing.gameSpeed
        },
        units: {
            totalUnits: units.totalUnits,
            uniqueUnits: units.uniqueUnits.size,
            unitCategories: units.unitCategories,
            mostUsedUnits: units.mostUsedUnits.slice(0, 5)
        }
    };
}

/**
 * 生成洞察
 */
function generateInsights(analysis) {
    const insights = [];
    const gameplay = analysis.gameplay;
    const performance = analysis.performance;
    const timing = analysis.timing;
    
    // 游戏强度洞察
    if (gameplay.intensity === 'extreme') {
        insights.push('这是一场高强度的激烈战斗，玩家表现活跃');
    } else if (gameplay.intensity === 'very_low') {
        insights.push('游戏强度较低，可能是休闲对局或新手玩家');
    }
    
    // 平衡性洞察
    if (gameplay.balance === 'very_unbalanced') {
        insights.push('游戏平衡性较差，存在明显的实力差距');
    } else if (gameplay.balance === 'very_balanced') {
        insights.push('游戏平衡性很好，玩家实力相近');
    }
    
    // 技能差距洞察
    if (gameplay.skillGap === 'extreme') {
        insights.push('玩家技能差距极大，可能存在高手vs新手的情况');
    }
    
    // 时间洞察
    if (timing.isLongGame) {
        insights.push('这是一场较长的游戏，可能涉及复杂的战术');
    } else if (timing.isShortGame) {
        insights.push('游戏时间较短，可能是快速对局');
    }
    
    // 效率洞察
    if (performance.efficiency.avgDamagePerMinute > 1000) {
        insights.push('平均伤害效率很高，玩家表现优秀');
    }
    
    return insights;
}

/**
 * 生成单位使用洞察
 */
function generateUnitInsights(analysis) {
    const insights = [];
    const units = analysis.units;
    
    // 单位使用多样性
    const avgUnitsPerPlayer = units.totalUnits / analysis.players.totalPlayers;
    if (avgUnitsPerPlayer > 10) {
        insights.push('玩家使用了大量单位，战术选择丰富');
    } else if (avgUnitsPerPlayer < 3) {
        insights.push('单位使用较少，可能是专注型战术');
    }
    
    // 单位类型分布
    const categoryCounts = units.unitCategories;
    const maxCategory = Object.entries(categoryCounts).reduce((a, b) => a[1] > b[1] ? a : b);
    insights.push(`最常用的单位类型是: ${maxCategory[0]} (${maxCategory[1]} 个)`);
    
    // 最常用单位
    if (units.mostUsedUnits.length > 0) {
        const topUnit = units.mostUsedUnits[0];
        insights.push(`最受欢迎的单位: ${topUnit.name} (使用 ${topUnit.usageCount} 次)`);
    }
    
    // 单位效率
    if (units.unitEfficiency.mostEfficient.length > 0) {
        const mostEfficient = units.unitEfficiency.mostEfficient[0];
        insights.push(`最高效单位: ${mostEfficient.name} (效率: ${mostEfficient.efficiency.toFixed(0)})`);
    }
    
    return insights;
}

// 导出函数
window.enhancedDataAnalysis = {
    analyzeMatchData,
    formatAnalysisResult,
    extractBasicInfo,
    extractPlayerStats,
    extractUnitUsageStats,
    extractGameplayAnalysis,
    extractPerformanceMetrics,
    extractTimingAnalysis,
    extractMapAnalysis,
    extractTrendAnalysis
};
