@echo off
chcp 65001 >nul
echo 🚀 启动完整的本地开发环境...
echo.

REM 检查 Python 是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到 Python，请先安装 Python
    echo 💡 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 📁 当前目录: %CD%
echo.
echo 🌐 将启动以下服务:
echo   1. 本地服务器 (端口 8000) - 提供 HTML 页面
echo   2. CORS 代理服务器 (端口 8080) - 解决跨域问题
echo.

echo 🚀 启动本地服务器...
start "本地服务器" cmd /k "python start-server.py"

echo ⏳ 等待 3 秒...
timeout /t 3 /nobreak >nul

echo 🚀 启动 CORS 代理服务器...
start "CORS 代理" cmd /k "python cors-proxy.py"

echo.
echo ✅ 所有服务已启动！
echo.
echo 📄 访问地址:
echo   - 比赛查询页面: http://localhost:8000/index.html
echo   - CORS 代理: http://localhost:8080/proxy/
echo.
echo 💡 提示: 页面会自动使用本地 CORS 代理
echo.
echo 🌐 正在自动打开浏览器...
start http://localhost:8000/index.html

echo.
echo ✅ 浏览器已打开！如果没有自动打开，请手动访问: http://localhost:8000/index.html
echo.
pause 