# 🎮 比赛选择功能说明

## 功能概述

现在 `unit-analysis-demo.html` 页面支持从API获取多场比赛数据，并允许用户选择特定比赛进行分析。

## 主要功能

### 1. 多行JSON解析
- 自动解析API返回的多行JSON格式数据
- 支持NDJSON（Newline Delimited JSON）格式
- 过滤出有效的比赛数据（`type: "match"`）

### 2. 比赛列表显示
- 显示所有获取到的比赛
- 每个比赛显示：
  - 比赛编号（fightId）
  - 比赛时间（EndTime）
  - 比赛结果（胜利/失败/平局）
- 支持点击选择比赛

### 3. 比赛编号搜索
- 输入框支持直接输入比赛编号
- 快速定位特定比赛
- 实时验证比赛编号是否存在

### 4. 自动分析
- 选择比赛后自动进行单位使用分析
- 保持原有的手动JSON输入功能

## 使用方法

### 从API获取数据
1. 输入17位Steam ID
2. 点击"🚀 从API获取数据"
3. 等待数据加载完成
4. 在比赛列表中选择要分析的比赛

### 通过比赛编号选择
1. 在比赛编号输入框中输入比赛ID（如：2733311）
2. 点击"🔍 选择比赛"
3. 系统会自动选择并分析该比赛

### 手动输入数据
- 仍然支持直接在文本框中输入JSON数据
- 支持单场比赛或多场比赛数据

## 技术实现

### 多行JSON解析
```javascript
function parseMultiLineJSON(rawText) {
    const lines = rawText.trim().split('\n');
    const matches = [];
    
    for (const line of lines) {
        if (line.trim()) {
            try {
                const data = JSON.parse(line);
                if (data.type === 'match' && data.data) {
                    matches.push(data);
                }
            } catch (parseError) {
                console.warn('跳过无效的JSON行:', line.substring(0, 100));
            }
        }
    }
    
    return matches;
}
```

### 比赛选择逻辑
```javascript
function selectMatch(matchIndex) {
    // 移除之前的选中状态
    document.querySelectorAll('.match-item').forEach(item => {
        item.classList.remove('selected');
    });
    
    // 设置选中的比赛
    selectedMatch = allMatches[matchIndex];
    
    // 自动分析选中的比赛
    setTimeout(() => {
        analyzeUnitData();
    }, 500);
}
```

## 界面元素

### 比赛选择区域
- **比赛编号输入框**：输入特定比赛编号
- **选择比赛按钮**：根据编号选择比赛
- **比赛列表**：显示所有可用比赛
- **提示信息**：使用说明

### 比赛列表项
- **比赛编号**：显示fightId
- **比赛时间**：格式化的时间显示
- **比赛结果**：带颜色的结果标签
- **悬停效果**：鼠标悬停时高亮
- **选中状态**：蓝色背景表示选中

## 数据格式支持

### API返回格式
```
{"type":"metadata","steamId":"76561198039354882","totalMatches":100,"processedMatches":0}
{"type":"match","data":{"fightId":"2733311",...}}
{"type":"match","data":{"fightId":"2733312",...}}
```

### 单场比赛格式
```json
{
  "type": "match",
  "data": {
    "fightId": "2733311",
    "EndTime": 1754905650,
    "result": "win",
    "Data": {
      "247": {
        "Name": "haxey",
        "UnitData": {...}
      }
    }
  }
}
```

## 错误处理

### 网络错误
- 显示友好的错误信息
- 区分不同类型的网络错误

### 数据解析错误
- 跳过无效的JSON行
- 记录警告信息但不中断处理

### 比赛选择错误
- 验证比赛编号是否存在
- 显示明确的错误提示

## 测试

可以使用 `test-match-selection.html` 页面测试比赛选择功能：

```bash
# 启动服务器
python start-server.py

# 访问测试页面
http://localhost:8000/test-match-selection.html
```

## 兼容性

- 保持与现有功能的完全兼容
- 支持原有的手动JSON输入
- 支持单场比赛和多场比赛数据
- 自动检测数据格式并相应处理

## 未来改进

1. **批量分析**：支持同时分析多场比赛
2. **比赛筛选**：按时间、结果等条件筛选
3. **数据缓存**：缓存已获取的比赛数据
4. **导出功能**：导出分析结果
5. **历史记录**：保存分析历史
