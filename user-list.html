<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户列表 - 在线状态监控</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>👥</text></svg>">
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/user-list.css">
</head>
<body>
    <div class="container">
        <!-- 导航区域 -->
        <div class="header">
            <h1>👥 用户列表</h1>
            <p>监控用户最后在线时间和活跃状态</p>
            <div class="nav-links">
                <a href="index.html" class="nav-link">🎮 比赛记录查询</a>
                <a href="user-list.html" class="nav-link active">👥 用户列表</a>
            </div>
        </div>

        <!-- 控制区域 -->
        <div class="controls-section">
            <div class="controls-row">
                <button class="start-btn" id="startBtn">
                    <span class="btn-icon">🚀</span>
                    <span class="btn-text">开始获取</span>
                </button>
                
                <button class="refresh-btn" id="refreshBtn" style="display: none;">
                    <span class="btn-icon">🔄</span>
                    <span class="btn-text">刷新数据</span>
                </button>
                
                <div class="sort-controls">
                    <label for="sortSelect">排序方式：</label>
                    <select id="sortSelect" class="sort-select">
                        <option value="lastOnline">最后在线时间</option>
                        <option value="name">用户名</option>
                        <option value="status">在线状态</option>
                    </select>
                    <button class="sort-order-btn" id="sortOrderBtn" title="切换排序顺序">
                        ⬇️
                    </button>
                </div>

                <div class="filter-controls">
                    <input 
                        type="text" 
                        class="filter-input" 
                        id="filterInput" 
                        placeholder="搜索用户名..."
                    >
                    <select id="statusFilter" class="status-filter">
                        <option value="all">所有状态</option>
                        <option value="online">在线</option>
                        <option value="recent">最近活跃</option>
                        <option value="offline">离线</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在获取用户数据，请稍候...</p>
            <div class="progress-container" id="progressContainer" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <p class="progress-text" id="progressText">处理中...</p>
            </div>
        </div>

        <!-- 错误信息 -->
        <div class="error" id="error"></div>

        <!-- 用户统计 -->
        <div class="user-stats" id="userStats" style="display: none;">
            <div class="stat-item">
                <span class="stat-label">总用户数：</span>
                <span class="stat-value" id="totalUsers">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">在线用户：</span>
                <span class="stat-value online" id="onlineUsers">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">最近活跃：</span>
                <span class="stat-value recent" id="recentUsers">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">离线用户：</span>
                <span class="stat-value offline" id="offlineUsers">0</span>
            </div>
        </div>

        <!-- 用户列表 -->
        <div class="user-list-container" id="userListContainer" style="display: none;">
            <div class="user-list" id="userList">
                <!-- 用户列表项将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 内联配置 -->
    <script>
        // API 基础配置
        window.API_BASE = 'https://v2202406227732275300.nicesrv.de:5000';

        // 数据处理配置
        window.DATA_CONFIG = {
            MAX_MATCHES_DISPLAY: 100,
            STREAMING_ENABLED: true,
            UPDATE_INTERVAL: 10,
            TIMEOUT: 60000
        };

        // 用户列表配置
        window.USER_LIST_CONFIG = {
            // 用户数据 - 监控列表（已去重）
            USERS: [
                { name: 'USMC70654', steamId: '76561198384214837' },
                { name: '「HiaGMI」Er1danus', steamId: '76561199228467153' },
                { name: 'アンテナ', steamId: '76561198226624556' },
                { name: 'OrangePekoe', steamId: '76561199801621565' },
                { name: 'Dinokangaroo', steamId: '76561199870369187' },
                { name: 'Nom1n', steamId: '76561198435942450' },
                { name: 'WaduSSYSK', steamId: '76561198386337123' },
                { name: 'Trenbolonezed CH', steamId: '76561198007869282' },
                { name: 'Arctemis', steamId: '76561198830814741' },
                { name: 'Darjeeling', steamId: '76561198255890785' },
                { name: 'Pen', steamId: '76561198157609957' },
                { name: '超绝陀螺旋转中', steamId: '76561198276863457' },
                { name: 'AC-130U Spooky II', steamId: '76561198150776378' },
                { name: '扣1送个大的', steamId: '76561199140563710' },
                { name: '13lade', steamId: '76561197970407686' },
                { name: 'NEW PLAYER', steamId: '76561199824803032' },
                { name: 'Metz', steamId: '76561198385375320' },
                { name: 'VanillaCat', steamId: '76561198411275719' },
                { name: 'Ark', steamId: '76561198063425835' },
                { name: 'WolfBite', steamId: '76561198253202550' },
                { name: '极炫丿灬皇朝|默白言', steamId: '76561198238696828' },
                { name: '极炫丿灬皇朝|覇世狅獅', steamId: '76561198134377658' },
                { name: 'Jörmungandr', steamId: '76561198282746946' },
                { name: 'anpromole', steamId: '76561199652673164' },
                { name: 'Demo', steamId: '76561198095185388' },
                { name: '陪玩教学局+Q6297448', steamId: '76561198410872750' },
                { name: '1024396833', steamId: '76561198263452783' },
                { name: 'NuclearEvergreen', steamId: '76561198319118907' },
                { name: 'Amatsukaze', steamId: '76561198402315182' },
                { name: '对面高地五个近坦', steamId: '76561199010806821' },
                { name: 'David "Uluru" Feller', steamId: '76561199802733513' },
                { name: '对面林子八个喷火', steamId: '76561198452392051' },
                { name: 'Pazuzu', steamId: '76561198292006331' },
                { name: 'Ben', steamId: '76561198070202261' },
                { name: 'RPGlancer', steamId: '76561198874561086' },
                { name: 'Rukuriri', steamId: '76561199877087022' }
            ],
            // 请求间隔（毫秒） - 调整为2秒，适应分批处理
            REQUEST_DELAY: 2000,
            // 缓存时间（毫秒）
            CACHE_DURATION: 5 * 60 * 1000, // 5分钟
            // 近期活跃阈值（小时）
            RECENT_ACTIVE_HOURS: 24
        };

        // 时间格式化配置
        window.USER_LIST_TIME_CONFIG = {
            // 相对时间阈值（毫秒）
            RELATIVE_TIME_THRESHOLDS: {
                MINUTE: 60 * 1000,      // 1分钟
                HOUR: 60 * 60 * 1000,   // 1小时
                DAY: 24 * 60 * 60 * 1000, // 1天
                WEEK: 7 * 24 * 60 * 60 * 1000 // 1周
            },
            // 日期格式化选项
            DATE_FORMAT: {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                timeZone: 'Asia/Shanghai'
            }
        };

        // DOM 元素 ID
        window.USER_LIST_DOM_IDS = {
            START_BTN: 'startBtn',
            REFRESH_BTN: 'refreshBtn',
            SORT_SELECT: 'sortSelect',
            SORT_ORDER_BTN: 'sortOrderBtn',
            FILTER_INPUT: 'filterInput',
            STATUS_FILTER: 'statusFilter',
            LOADING: 'loading',
            ERROR: 'error',
            USER_STATS: 'userStats',
            TOTAL_USERS: 'totalUsers',
            ONLINE_USERS: 'onlineUsers',
            RECENT_USERS: 'recentUsers',
            OFFLINE_USERS: 'offlineUsers',
            USER_LIST_CONTAINER: 'userListContainer',
            USER_LIST: 'userList',
            PROGRESS_CONTAINER: 'progressContainer',
            PROGRESS_FILL: 'progressFill',
            PROGRESS_TEXT: 'progressText'
        };

        // API 端点
        window.USER_LIST_API_ENDPOINTS = {
            RECENT_MATCHES: (steamId) => `${window.API_BASE}/statistic/getRecentMatches/${steamId}`
        };

        // 流式API配置
        window.STREAMING_API_CONFIG = {
            TIMEOUT: 20000,           // 20秒超时 - 适应直接fetch策略
            MAX_RETRIES: 1,           // 1次重试
            RETRY_DELAY_BASE: 1000    // 1秒重试延迟
        };

        // 代理配置 - 使用本地CORS代理
        window.PROXY_CONFIG = {
            LOCAL: 'http://localhost:8080/proxy/'
        };

        console.log('✅ 用户列表配置已加载 - 将使用本地CORS代理');
        console.log('📡 本地代理地址:', window.PROXY_CONFIG.LOCAL);
    </script>

    <!-- JavaScript 文件 -->
    <script src="js/user-list-api-v2.js?v=2.4"></script>
    <script src="js/user-list-ui.js?v=2.4"></script>
    <script src="js/user-list-app.js?v=2.4"></script>
</body>
</html>