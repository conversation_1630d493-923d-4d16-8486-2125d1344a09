/**
 * 增强数据分析模块测试脚本
 */

// 模拟测试数据
const testMatchData = {
    "type": "match",
    "data": {
        "fightId": 12345,
        "EndTime": 1703123456,
        "MapId": 5,
        "TotalPlayTimeInSec": 480,
        "ratingChange": 25,
        "Data": {
            "76561198012345678": {
                "DamageDealt": 2500,
                "Kills": 8,
                "Deaths": 2,
                "PlayTimeInSec": 480
            },
            "76561198087654321": {
                "DamageDealt": 1800,
                "Kills": 5,
                "Deaths": 4,
                "PlayTimeInSec": 480
            },
            "76561198011111111": {
                "DamageDealt": 1200,
                "Kills": 3,
                "Deaths": 6,
                "PlayTimeInSec": 480
            },
            "76561198022222222": {
                "DamageDealt": 800,
                "Kills": 2,
                "Deaths": 7,
                "PlayTimeInSec": 480
            }
        }
    }
};

// 模拟地图名称函数（如果utils.js不可用）
if (typeof getMapName === 'undefined') {
    window.getMapName = function(mapId) {
        const MAP_NAMES = [
            '', '黑海岸', '半岛', '水坝', '寒水港',
            '蜿蜒之河', '空军基地', '石油热潮', '死亡公路', '死亡中心',
            '叶尔加瓦', '冰雪城堡', '苏瓦乌基走廊', '克莱佩达裂谷', '鲁达森林',
            '风啸湾', '切尔尼亚霍夫斯克', '核电站', '加里宁格勒',
            '', '', '', '加里宁格勒'
        ];
        if (!mapId || mapId <= 0) return '未知地图';
        if (mapId <= MAP_NAMES.length) {
            return MAP_NAMES[mapId - 1];
        }
        return `地图${mapId}`;
    };
}

// 模拟时长格式化函数（如果utils.js不可用）
if (typeof formatDuration === 'undefined') {
    window.formatDuration = function(seconds) {
        if (!seconds || seconds <= 0) return '--';
        const minutes = Math.floor(seconds / 60);
        if (minutes < 60) {
            return `${minutes}分`;
        } else {
            const hours = Math.floor(minutes / 60);
            const remainingMinutes = minutes % 60;
            return remainingMinutes > 0 ? `${hours}h${remainingMinutes}m` : `${hours}h`;
        }
    };
}

// 测试函数
function testEnhancedDataAnalysis() {
    console.log('🧪 开始测试增强数据分析模块...');
    
    try {
        // 检查模块是否可用
        if (typeof window.enhancedDataAnalysis === 'undefined') {
            console.error('❌ 增强数据分析模块未加载');
            return;
        }
        
        console.log('✅ 增强数据分析模块已加载');
        
        // 测试数据分析
        console.log('🔍 分析测试数据...');
        const analysis = window.enhancedDataAnalysis.analyzeMatchData(testMatchData);
        
        console.log('📊 分析结果:');
        console.log('基础信息:', analysis.basic);
        console.log('玩家统计:', analysis.players);
        console.log('游戏分析:', analysis.gameplay);
        console.log('性能指标:', analysis.performance);
        console.log('时间分析:', analysis.timing);
        console.log('地图分析:', analysis.map);
        
        // 测试格式化结果
        console.log('📝 格式化结果...');
        const formattedResult = window.enhancedDataAnalysis.formatAnalysisResult(analysis);
        
        console.log('摘要:', formattedResult.summary);
        console.log('详细信息:', formattedResult.details);
        console.log('洞察:', formattedResult.insights);
        
        // 验证关键指标
        console.log('🔍 验证关键指标...');
        
        // 验证基础信息
        if (analysis.basic.matchId === 12345) console.log('✅ 比赛ID正确');
        if (analysis.basic.mapName === '蜿蜒之河') console.log('✅ 地图名称正确');
        if (analysis.basic.result === 'win') console.log('✅ 胜负判断正确');
        if (analysis.basic.playerCount === 4) console.log('✅ 玩家数量正确');
        
        // 验证玩家统计
        if (analysis.players.totalPlayers === 4) console.log('✅ 总玩家数正确');
        if (analysis.players.damageStats.totalDamage === 6300) console.log('✅ 总伤害正确');
        if (analysis.players.damageStats.maxDamage === 2500) console.log('✅ 最高伤害正确');
        if (analysis.players.killStats.totalKills === 18) console.log('✅ 总击杀正确');
        
        // 验证游戏分析
        if (analysis.gameplay.intensity === 'high') console.log('✅ 游戏强度分析正确');
        if (analysis.gameplay.balance === 'unbalanced') console.log('✅ 平衡性分析正确');
        
        // 验证时间分析
        if (analysis.timing.durationFormatted === '8分') console.log('✅ 时长格式化正确');
        if (analysis.timing.gameSpeed === 'normal') console.log('✅ 游戏速度分析正确');
        
        console.log('🎉 所有测试通过！');
        
        // 显示详细分析结果
        console.log('\n📋 完整分析报告:');
        console.log('='.repeat(50));
        
        console.log('🏆 比赛摘要:');
        console.log(`  比赛: ${formattedResult.summary.matchInfo}`);
        console.log(`  玩家: ${formattedResult.summary.playerCount}`);
        console.log(`  强度: ${formattedResult.summary.intensity}`);
        console.log(`  平衡: ${formattedResult.summary.balance}`);
        
        console.log('\n👥 玩家统计:');
        console.log(`  总伤害: ${analysis.players.damageStats.totalDamage}`);
        console.log(`  平均伤害: ${analysis.players.damageStats.averageDamage}`);
        console.log(`  最高伤害: ${analysis.players.damageStats.maxDamage}`);
        console.log(`  总击杀: ${analysis.players.killStats.totalKills}`);
        
        console.log('\n🎮 游戏分析:');
        console.log(`  强度: ${analysis.gameplay.intensity}`);
        console.log(`  平衡性: ${analysis.gameplay.balance}`);
        console.log(`  技能差距: ${analysis.gameplay.skillGap}`);
        console.log(`  参与度: ${analysis.gameplay.engagement}`);
        
        console.log('\n⚡ 性能指标:');
        console.log(`  平均伤害/分钟: ${analysis.performance.efficiency.avgDamagePerMinute}`);
        console.log(`  平均击杀/分钟: ${analysis.performance.efficiency.avgKillsPerMinute}`);
        console.log(`  平均伤害/击杀: ${analysis.performance.efficiency.avgDamagePerKill}`);
        console.log(`  最高玩家占比: ${analysis.performance.dominance.topPlayerShare}%`);
        
        console.log('\n⏰ 时间分析:');
        console.log(`  时长: ${analysis.timing.durationFormatted}`);
        console.log(`  时间: ${analysis.timing.timeOfDay}`);
        console.log(`  星期: ${analysis.timing.dayOfWeek}`);
        console.log(`  速度: ${analysis.timing.gameSpeed}`);
        
        console.log('\n🗺️ 地图分析:');
        console.log(`  地图: ${analysis.map.mapName}`);
        console.log(`  类型: ${analysis.map.mapType}`);
        
        console.log('\n💡 数据洞察:');
        formattedResult.insights.forEach((insight, index) => {
            console.log(`  ${index + 1}. ${insight}`);
        });
        
        console.log('='.repeat(50));
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
    }
}

// 如果直接运行此脚本，执行测试
if (typeof window !== 'undefined') {
    // 在浏览器环境中，等待模块加载
    window.addEventListener('load', function() {
        setTimeout(testEnhancedDataAnalysis, 2000);
    });
} else {
    // 在Node.js环境中直接执行
    console.log('请在浏览器环境中运行此测试');
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testEnhancedDataAnalysis, testMatchData };
}

