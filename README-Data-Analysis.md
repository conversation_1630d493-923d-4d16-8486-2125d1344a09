# 增强数据分析模块

## 概述

这个增强数据分析模块可以从比赛数据中提取更多有用的信息，提供深入的游戏分析和洞察。

## 功能特性

### 🔍 基础信息提取
- **比赛ID**: 唯一标识符
- **结束时间**: 比赛结束的时间戳
- **地图信息**: 地图ID和名称
- **游戏时长**: 比赛持续时间
- **评分变化**: 胜负判断依据
- **玩家数量**: 参与比赛的玩家总数

### 👥 玩家统计分析
- **伤害统计**: 总伤害、平均伤害、最高/最低伤害
- **击杀统计**: 总击杀、平均击杀、最高/最低击杀
- **死亡统计**: 总死亡、平均死亡、最高/最低死亡
- **伤害分布**: 按伤害等级分类统计
- **玩家详情**: 每个玩家的详细表现数据
- **表现评分**: 基于伤害、K/D比、效率的综合评分

### 🎮 游戏玩法分析
- **游戏强度**: 基于平均伤害和击杀的强度评估
- **平衡性**: 玩家间实力差距的统计分析
- **技能差距**: 最高和最低表现玩家的差距分析
- **参与度**: 活跃玩家比例统计

### ⚡ 性能指标
- **效率指标**: 每分钟伤害、每分钟击杀、每次击杀伤害
- **一致性**: 玩家表现的一致性分析
- **统治力**: 最高表现玩家的统治程度

### ⏰ 时间分析
- **游戏时长**: 格式化显示
- **时间分类**: 一天中的时间段（早晨/下午/晚上/夜晚）
- **星期分析**: 星期几
- **游戏速度**: 基于击杀频率的速度评估

### 🗺️ 地图分析
- **地图名称**: 根据ID获取地图名称
- **地图分类**: 小/中/大地图分类
- **受欢迎程度**: 地图使用频率统计

### 💡 智能洞察
- **自动生成**: 基于数据分析自动生成洞察
- **多维度**: 从不同角度提供分析建议
- **实用性强**: 提供具体的改进建议

## 使用方法

### 1. 引入模块

```html
<script src="js/utils.js"></script>
<script src="js/enhanced-data-analysis.js"></script>
```

### 2. 分析数据

```javascript
// 准备比赛数据
const matchData = {
    "type": "match",
    "data": {
        "fightId": 12345,
        "EndTime": 1703123456,
        "MapId": 5,
        "TotalPlayTimeInSec": 480,
        "ratingChange": 25,
        "Data": {
            "76561198012345678": {
                "DamageDealt": 2500,
                "Kills": 8,
                "Deaths": 2,
                "PlayTimeInSec": 480
            }
            // ... 更多玩家数据
        }
    }
};

// 执行分析
const analysis = window.enhancedDataAnalysis.analyzeMatchData(matchData);

// 格式化结果
const formattedResult = window.enhancedDataAnalysis.formatAnalysisResult(analysis);
```

### 3. 查看结果

```javascript
// 基础信息
console.log('比赛信息:', analysis.basic);

// 玩家统计
console.log('玩家统计:', analysis.players);

// 游戏分析
console.log('游戏分析:', analysis.gameplay);

// 性能指标
console.log('性能指标:', analysis.performance);

// 时间分析
console.log('时间分析:', analysis.timing);

// 地图分析
console.log('地图分析:', analysis.map);

// 格式化摘要
console.log('摘要:', formattedResult.summary);

// 洞察
console.log('洞察:', formattedResult.insights);
```

## 数据格式

### 输入数据格式

```javascript
{
    "type": "match",
    "data": {
        "fightId": number,           // 比赛ID
        "EndTime": number,           // 结束时间戳（秒）
        "MapId": number,             // 地图ID
        "TotalPlayTimeInSec": number, // 游戏时长（秒）
        "ratingChange": number,      // 评分变化
        "Data": {                    // 玩家数据
            "steamId": {
                "DamageDealt": number,  // 伤害
                "Kills": number,        // 击杀
                "Deaths": number,       // 死亡
                "PlayTimeInSec": number // 游戏时长
            }
        }
    }
}
```

### 输出数据格式

```javascript
{
    "basic": {
        "matchId": number,
        "endTime": number,
        "mapId": number,
        "mapName": string,
        "duration": number,
        "ratingChange": number,
        "result": "win" | "loss",
        "playerCount": number
    },
    "players": {
        "totalPlayers": number,
        "playerDetails": [...],
        "damageStats": {...},
        "killStats": {...},
        "deathStats": {...}
    },
    "gameplay": {
        "intensity": string,
        "balance": string,
        "skillGap": string,
        "engagement": string
    },
    "performance": {
        "efficiency": {...},
        "consistency": {...},
        "dominance": {...}
    },
    "timing": {
        "endTime": number,
        "duration": number,
        "durationFormatted": string,
        "timeOfDay": string,
        "dayOfWeek": string,
        "isLongGame": boolean,
        "isShortGame": boolean,
        "gameSpeed": string
    },
    "map": {
        "mapId": number,
        "mapName": string,
        "mapType": string,
        "mapPopularity": string
    },
    "trends": {
        "ratingTrend": string,
        "performanceTrend": string,
        "mapPreference": string
    }
}
```

## 分析指标说明

### 游戏强度等级
- **extreme**: 极高强度（平均伤害≥2000且平均击杀≥5）
- **high**: 高强度（平均伤害≥1500且平均击杀≥3）
- **medium**: 中等强度（平均伤害≥1000且平均击杀≥2）
- **low**: 低强度（平均伤害≥500且平均击杀≥1）
- **very_low**: 极低强度（其他情况）

### 平衡性等级
- **very_balanced**: 非常平衡（变异系数≤0.3）
- **balanced**: 平衡（变异系数≤0.5）
- **unbalanced**: 不平衡（变异系数≤0.7）
- **very_unbalanced**: 非常不平衡（变异系数>0.7）

### 技能差距等级
- **low**: 差距小（差距比≤1）
- **medium**: 差距中等（差距比≤2）
- **high**: 差距大（差距比≤3）
- **extreme**: 差距极大（差距比>3）

### 参与度等级
- **very_high**: 参与度很高（≥90%）
- **high**: 参与度高（≥80%）
- **medium**: 参与度中等（≥70%）
- **low**: 参与度低（≥60%）
- **very_low**: 参与度很低（<60%）

### 游戏速度等级
- **very_fast**: 极快（每分钟击杀≥2）
- **fast**: 快（每分钟击杀≥1.5）
- **normal**: 正常（每分钟击杀≥1）
- **slow**: 慢（每分钟击杀≥0.5）
- **very_slow**: 极慢（每分钟击杀<0.5）

## 演示页面

访问 `data-analysis-demo.html` 查看完整的演示效果，包括：
- 交互式数据输入
- 实时分析结果
- 可视化指标展示
- 智能洞察生成

## 测试

运行 `test-data-analysis.js` 进行自动化测试，验证模块功能。

## 扩展功能

### 自定义分析
可以通过修改 `enhanced-data-analysis.js` 来添加自定义分析逻辑：

```javascript
// 添加自定义分析函数
function customAnalysis(matchData) {
    // 自定义分析逻辑
    return {
        customMetric: value,
        customInsight: insight
    };
}

// 集成到主分析流程
function analyzeMatchData(matchData) {
    const analysis = {
        // ... 现有分析
        custom: customAnalysis(matchData)
    };
    return analysis;
}
```

### 数据可视化
可以基于分析结果创建图表和可视化：

```javascript
// 创建伤害分布图表
function createDamageChart(analysis) {
    const damageData = analysis.players.damageStats.damageDistribution;
    // 使用Chart.js或其他图表库
}

// 创建性能雷达图
function createPerformanceRadar(analysis) {
    const performanceData = analysis.performance;
    // 创建雷达图
}
```

## 注意事项

1. **数据完整性**: 确保输入数据包含必要的字段
2. **时间戳格式**: 支持秒级和毫秒级时间戳
3. **地图ID**: 确保地图ID在有效范围内
4. **性能考虑**: 大量数据处理时注意性能优化

## 更新日志

### v1.0.0
- 初始版本发布
- 基础信息提取
- 玩家统计分析
- 游戏玩法分析
- 性能指标计算
- 时间分析
- 地图分析
- 智能洞察生成

