# 战术档案分析功能

## 概述

战术档案分析是一个独立的页面功能，用于分析玩家的战术偏好和单位使用模式。通过API获取战局记录，为每个玩家建立详细的战术档案，分析他们依赖哪些单位制造伤害。

## 功能特点

### 🎯 核心功能
- **API数据获取**: 支持从指定API获取比赛数据
- **玩家档案建立**: 为每个玩家创建详细的战术档案
- **单位伤害分析**: 分析玩家依赖的主要伤害来源单位
- **战术洞察**: 提供智能的战术分析和建议

### 📊 分析维度
- **总伤害统计**: 玩家在所有比赛中的总伤害
- **平均伤害**: 每场比赛的平均伤害输出
- **伤害效率**: 伤害输出与受到伤害的比率
- **单位使用频率**: 各单位的使用次数和效果
- **分类偏好**: 玩家对不同单位类型的偏好

### 🎨 界面特性
- **现代化设计**: 渐变背景和卡片式布局
- **响应式布局**: 支持桌面和移动设备
- **交互式筛选**: 支持按不同条件排序和筛选
- **单位链接**: 直接链接到单位详情页面

## 使用方法

### 1. 启动服务
```bash
# 启动本地HTTP服务器
python start-server.py

# 启动CORS代理（如果需要）
python cors-proxy.py
```

### 2. 访问页面
打开浏览器访问：`http://localhost:8000/tactical-profile.html`

### 3. 输入参数
- **Steam ID**: 输入要分析的玩家Steam ID
- **分析场数**: 设置要分析的最近比赛场数（1-100）

### 4. 获取分析
点击"获取战术档案"按钮开始分析

## 数据展示

### 玩家档案卡片
每个玩家的档案包含：
- **基本信息**: 玩家名称、ID、队伍
- **统计数据**: 比赛场数、总伤害、平均伤害、伤害效率
- **主要伤害来源**: 前5个最有效的单位
- **战术洞察**: 智能分析玩家的战术特点
- **比赛历史**: 最近5场比赛的记录

### 单位分析
每个单位显示：
- **单位名称**: 可点击链接到详情页面
- **总伤害**: 该单位造成的总伤害
- **使用次数**: 该单位被使用的次数
- **击杀数**: 该单位获得的击杀
- **平均伤害**: 每次使用的平均伤害
- **单位分类**: 步兵、车辆、空中等

### 筛选和排序
- **排序方式**: 按总伤害、比赛场数、平均伤害、玩家名称排序
- **显示筛选**: 显示所有玩家、伤害前10、频繁玩家

## 技术实现

### 前端技术
- **HTML5**: 语义化标签和现代HTML特性
- **CSS3**: Flexbox、Grid、渐变、动画效果
- **JavaScript**: ES6+语法、异步处理、DOM操作

### 数据处理
- **API集成**: 支持直接API调用和代理转发
- **JSON解析**: 处理多行JSON格式的API响应
- **数据聚合**: 多场比赛数据的统计和分析
- **单位映射**: 完整的单位ID到名称和分类的映射

### 文件结构
```
tactical-profile.html          # 主页面文件
js/
├── unit-analysis.js          # 单位分析核心逻辑
└── unit-mapping.js           # 单位映射配置
README-Tactical-Profile.md    # 功能说明文档
```

## 单位映射

系统包含完整的单位映射配置：
- **步兵单位**: 82-92 (步兵、机枪手、狙击手等)
- **车辆单位**: 320-329 (坦克、装甲车、工程车等)
- **空中单位**: 448-455 (战斗机、轰炸机、直升机等)
- **支援单位**: 600-605 (雷达站、通信站、补给站等)
- **防御单位**: 700-707 (防空炮、碉堡、地雷等)
- **海军单位**: 800-807 (驱逐舰、巡洋舰、航母等)

## 扩展功能

### 可能的改进方向
1. **历史趋势分析**: 分析玩家战术随时间的变化
2. **对手对比**: 与特定对手的战术对比
3. **地图分析**: 不同地图上的战术偏好
4. **团队协作**: 分析团队中的战术配合
5. **预测功能**: 预测玩家可能采用的战术

### 数据导出
- 支持将分析结果导出为PDF或Excel
- 生成详细的战术报告
- 支持批量分析多个玩家

## 故障排除

### 常见问题
1. **网络连接失败**: 确保代理服务器已启动
2. **数据解析错误**: 检查API返回的数据格式
3. **单位显示异常**: 确认单位映射配置正确

### 调试信息
- 浏览器控制台会显示详细的错误信息
- 网络请求状态会在页面上显示
- 数据解析过程有详细的日志输出

## 更新日志

### v1.0.0 (当前版本)
- ✅ 基础战术档案分析功能
- ✅ API数据获取和解析
- ✅ 玩家档案展示
- ✅ 单位伤害分析
- ✅ 战术洞察功能
- ✅ 响应式界面设计
- ✅ 完整的单位映射

---

*战术档案分析功能为游戏玩家提供了深入了解对手战术偏好的强大工具，帮助制定更有效的应对策略。*
