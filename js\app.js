/**
 * 主应用模块
 * 包含应用初始化和模块协调逻辑
 */

/**
 * 应用状态管理
 */
const AppState = {
    isInitialized: false,
    currentSteamId: null,
    isLoading: false,
    lastSearchTime: null
};

/**
 * 应用配置验证
 */
function validateAppConfiguration() {
    const requiredConfigs = [
        'API_BASE',
        'DATA_CONFIG',
        'DOM_IDS',
        'API_ENDPOINTS',
        'ERROR_MESSAGES',
        'TIME_CONFIG',
        'MATCH_RESULT_CONFIG',
        'EXTERNAL_LINKS'
    ];
    
    console.log('🔍 开始验证应用配置...');
    console.log('🔍 当前 window 对象中的配置:', {
        API_BASE: window.API_BASE,
        DATA_CONFIG: window.DATA_CONFIG,
        DOM_IDS: window.DOM_IDS,
        API_ENDPOINTS: window.API_ENDPOINTS,
        ERROR_MESSAGES: window.ERROR_MESSAGES,
        TIME_CONFIG: window.TIME_CONFIG,
        MATCH_RESULT_CONFIG: window.MATCH_RESULT_CONFIG,
        EXTERNAL_LINKS: window.EXTERNAL_LINKS
    });
    
    const missingConfigs = requiredConfigs.filter(config => typeof window[config] === 'undefined');
    
    if (missingConfigs.length > 0) {
        console.error('❌ 缺少必要的配置:', missingConfigs);
        console.error('❌ 当前可用的配置:', Object.keys(window).filter(key => requiredConfigs.includes(key)));
        throw new Error(`应用配置不完整，缺少: ${missingConfigs.join(', ')}`);
    }
    
    console.log('✅ 应用配置验证通过');
}

/**
 * 模块依赖检查
 */
function checkModuleDependencies() {
    const requiredFunctions = [
        'formatTime',
        'getFullTimeString',
        'formatDuration',
        'validateSteamId',
        'validatePlayerStats',
        'validateMatchData',
        'processPlayerStats',
        'processMatchData',
        'tryFixJsonString',
        'handleApiError',
        'getPlayerStats',
        'getRecentMatches',
        'processStreamingData',
        'getRecentMatchesFallback',
        'showError',
        'showCorsNotice',
        'hideCorsNotice',
        'showLoading',
        'hideLoading',
        'updateProgress',
        'renderPlayerStats',
        'renderMatchesIncremental',
        'renderMatches',
        'showResults',
        'hideResults',
        'clearStreamingStatus',
        'resetUI',
        'handleMatchIdClick',
        'handleFormSubmit',
        'handleMatchLinkClick',
        'handleSearch',
        'initializeEventListeners',
        'handleUrlParameters',
        'initializePage'
    ];
    
    const missingFunctions = requiredFunctions.filter(func => typeof window[func] !== 'function');
    
    if (missingFunctions.length > 0) {
        console.error('❌ 缺少必要的函数:', missingFunctions);
        throw new Error(`模块依赖不完整，缺少函数: ${missingFunctions.join(', ')}`);
    }
    
    console.log('✅ 模块依赖检查通过');
}

/**
 * 应用初始化
 */
function initializeApp() {
    try {
        console.log('🚀 开始应用初始化...');
        
        // 验证应用配置
        validateAppConfiguration();
        
        // 检查模块依赖
        checkModuleDependencies();
        
        // 初始化页面
        initializePage();
        
        // 标记应用已初始化
        AppState.isInitialized = true;
        
        console.log('✅ 应用初始化完成');
        
        // 触发应用就绪事件
        window.dispatchEvent(new CustomEvent('appReady', {
            detail: { timestamp: Date.now() }
        }));
        
    } catch (error) {
        console.error('❌ 应用初始化失败:', error);
        
        // 显示错误信息
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #f5c6cb;
            z-index: 10000;
            max-width: 500px;
            text-align: center;
        `;
        errorDiv.innerHTML = `
            <h3>应用初始化失败</h3>
            <p>${error.message}</p>
            <p>请刷新页面重试，或联系管理员。</p>
        `;
        document.body.appendChild(errorDiv);
        
        throw error;
    }
}

/**
 * 应用状态重置
 */
function resetAppState() {
    AppState.currentSteamId = null;
    AppState.isLoading = false;
    AppState.lastSearchTime = null;
    resetUI();
}

/**
 * 应用状态获取
 */
function getAppState() {
    return { ...AppState };
}

/**
 * 应用状态更新
 * @param {object} updates - 要更新的状态
 */
function updateAppState(updates) {
    Object.assign(AppState, updates);
}

/**
 * 应用性能监控
 */
function startPerformanceMonitoring() {
    if (window.performance && window.performance.mark) {
        window.performance.mark('app-start');
        
        window.addEventListener('appReady', () => {
            window.performance.mark('app-ready');
            window.performance.measure('app-initialization', 'app-start', 'app-ready');
            
            const measure = window.performance.getEntriesByName('app-initialization')[0];
            console.log(`📊 应用初始化耗时: ${measure.duration.toFixed(2)}ms`);
        });
    }
}

/**
 * 错误处理增强
 */
function enhanceErrorHandling() {
    // 全局错误处理
    window.addEventListener('error', (event) => {
        console.error('全局错误:', event.error);
        
        if (AppState.isInitialized) {
            showError('应用发生错误，请刷新页面重试');
        }
    });
    
    // 未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
        console.error('未处理的Promise拒绝:', event.reason);
        
        if (AppState.isInitialized) {
            showError('网络请求失败，请检查网络连接');
        }
    });
}

/**
 * 开发模式增强
 */
function enableDevelopmentMode() {
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        console.log('🔧 开发模式已启用');
        
        // 添加全局调试对象
        window.AppDebug = {
            state: () => getAppState(),
            reset: () => resetAppState(),
            test: {
                formatTime: (timestamp) => formatTime(timestamp),
                validateSteamId: (id) => validateSteamId(id),
                renderTest: () => {
                    const testData = {
                        name: '测试玩家',
                        winsCount: 100,
                        losesCount: 50,
                        fightsCount: 150,
                        kdRatio: 2.0
                    };
                    renderPlayerStats(testData);
                    showResults();
                }
            }
        };
        
        console.log('🔧 调试工具已加载，使用 window.AppDebug 访问');
    }
}

/**
 * 应用启动
 */
function startApp() {
    console.log('🎮 比赛记录查询应用启动中...');
    
    // 启动性能监控
    startPerformanceMonitoring();
    
    // 增强错误处理
    enhanceErrorHandling();
    
    // 启用开发模式
    enableDevelopmentMode();
    
    // 等待DOM加载完成后初始化应用
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeApp);
    } else {
        initializeApp();
    }
}

// 启动应用
startApp(); 