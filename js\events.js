/**
 * 事件处理模块
 * 包含表单提交、点击事件和页面初始化逻辑
 */

/**
 * 处理比赛ID点击事件
 * @param {string} matchId - 比赛ID
 */
function handleMatchIdClick(matchId) {
    const url = EXTERNAL_LINKS.MATCH_DETAILS(matchId);
    console.log(`🔗 打开比赛详情页面: ${url}`);
    window.open(url, '_blank');
}

/**
 * 处理表单提交事件
 * @param {Event} e - 表单提交事件
 */
async function handleFormSubmit(e) {
    e.preventDefault();
    
    const steamId = domElements.steamIdInput.value.trim();
    if (!steamId) {
        showError(ERROR_MESSAGES.EMPTY_STEAM_ID);
        return;
    }

    // Steam ID 格式验证
    if (!validateSteamId(steamId)) {
        showError(ERROR_MESSAGES.INVALID_STEAM_ID);
        return;
    }

    await handleSearch(steamId);
}

/**
 * 处理比赛ID链接点击事件
 * @param {Event} e - 点击事件
 */
function handleMatchLinkClick(e) {
    if (e.target.classList.contains('match-id-link')) {
        e.preventDefault();
        const matchId = e.target.textContent.trim();
        handleMatchIdClick(matchId);
    }
}

/**
 * 处理查询逻辑
 * @param {string} steamId - Steam ID
 */
async function handleSearch(steamId) {
    showLoading();
    domElements.searchBtn.disabled = true;

    try {
        console.log(`🔍 开始查询 Steam ID: ${steamId}`);
        
        // 先获取玩家统计信息
        const playerStats = await getPlayerStats(steamId).catch(err => {
            handleApiError(err, '玩家统计信息');
            return null;
        });
        
        // 渲染玩家统计信息
        renderPlayerStats(playerStats);
        
        // 获取比赛记录（流式API）
        console.log('📡 开始获取比赛记录（流式API）...');
        const matchesData = await getRecentMatches(steamId).catch(err => {
            handleApiError(err, '比赛记录');
            return null;
        });

        console.log('📊 数据获取完成，开始最终渲染...');

        // 最终渲染比赛记录
        if (matchesData) {
            renderMatches(matchesData);
        }
        
        showResults();
        console.log('✅ 查询完成');
    } catch (err) {
        console.error('查询错误:', err);
        const errorMessage = err.message || '查询失败，请检查 Steam ID 是否正确';
        
        // 检查是否是 CORS 相关错误
        if (errorMessage.includes('CORS')) {
            showCorsNotice();
        } else {
            showError(errorMessage);
        }
    } finally {
        hideLoading();
        domElements.searchBtn.disabled = false;
    }
}

/**
 * 初始化事件监听器
 */
function initializeEventListeners() {
    // 表单提交事件
    domElements.searchForm.addEventListener('submit', handleFormSubmit);
    
    // 比赛ID链接点击事件（事件委托）
    document.addEventListener('click', handleMatchLinkClick);
}

/**
 * 从URL参数中获取Steam ID并自动查询
 */
function handleUrlParameters() {
    const urlParams = new URLSearchParams(window.location.search);
    const steamIdFromUrl = urlParams.get('steamid');
    
    if (steamIdFromUrl) {
        domElements.steamIdInput.value = steamIdFromUrl;
        handleSearch(steamIdFromUrl);
    }
}

/**
 * 页面初始化
 */
function initializePage() {
    console.log('🚀 初始化页面...');
    
    // 初始化DOM元素引用
    initializeDOMElements();
    
    // 初始化事件监听器
    initializeEventListeners();
    
    // 处理URL参数
    handleUrlParameters();
    
    console.log('✅ 页面初始化完成');
} 