/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    min-height: 100vh;
    padding: 10px; /* 减少body padding，从20px改为10px */
}

/* 容器样式 */
.container {
    max-width: 95vw; /* 使用视口宽度的95%，从1200px改为95vw */
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    min-width: 1200px; /* 设置最小宽度，确保在小屏幕上不会太窄 */
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px; /* 减少padding，从30px改为20px 30px */
    text-align: center;
}

.header h1 {
    font-size: 2.2rem; /* 稍微减小字体，从2.5rem改为2.2rem */
    margin-bottom: 8px; /* 减少margin，从10px改为8px */
    font-weight: 300;
}

.header p {
    font-size: 1rem; /* 稍微减小字体，从1.1rem改为1rem */
    opacity: 0.9;
}

/* 搜索区域样式 */
.search-section {
    padding: 40px;
    background: #f8f9fa;
}

.search-form {
    display: flex;
    gap: 15px;
    max-width: 600px;
    margin: 0 auto;
}

.search-input {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-btn {
    padding: 15px 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.search-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 加载状态样式 */
.loading {
    display: none;
    text-align: center;
    padding: 40px;
}

.loading.show {
    display: block;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 进度条样式 */
.progress-container {
    margin-top: 20px;
    width: 100%;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
}

/* 更多比赛提示样式 */
.more-matches-notice {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-top: 15px;
    border: 1px solid #e9ecef;
}

.more-matches-notice p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

/* 错误信息样式 */
.error {
    display: none;
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 10px;
    margin: 20px 0;
    border: 1px solid #f5c6cb;
}

.error.show {
    display: block;
}

/* CORS 问题说明样式 */
.cors-notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    margin: 20px 0;
    padding: 20px;
}

.cors-notice.show {
    display: block;
}

.notice-content h3 {
    color: #856404;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.notice-content p {
    color: #856404;
    margin-bottom: 15px;
    line-height: 1.6;
}

.solutions {
    display: grid;
    gap: 20px;
    margin-top: 20px;
}

.solution {
    background: rgba(255, 255, 255, 0.5);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.solution h4 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1rem;
}

.solution p {
    color: #666;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.solution code {
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    color: #e83e8c;
}

/* 结果区域样式 */
.results {
    padding: 40px;
}

/* 玩家信息样式 */
.player-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    text-align: center;
}

.player-info h2 {
    font-size: 2rem;
    margin-bottom: 10px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* 比赛区域样式 */
.matches-section {
    margin-top: 30px;
}

.matches-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.matches-header h3 {
    font-size: 1.5rem;
    color: #333;
}

.match-count {
    background: #667eea;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.matches-list {
    display: grid;
    gap: 12px;
}

/* 比赛项目样式 */
.match-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.match-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
}

.match-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.match-result {
    display: flex;
    align-items: center;
    gap: 10px;
}

.result-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.result-win {
    background: #28a745;
}

.result-loss {
    background: #dc3545;
}

.result-text {
    font-weight: 600;
    font-size: 1.1rem;
}

.result-win .result-text {
    color: #28a745;
}

.result-loss .result-text {
    color: #dc3545;
}

/* 高伤害标识样式 */
.high-damage-badge {
    display: inline-block;
    margin-left: 8px;
    font-size: 1.2rem;
    animation: fire-pulse 2s infinite;
    filter: drop-shadow(0 0 4px rgba(255, 69, 0, 0.6));
}

@keyframes fire-pulse {
    0%, 100% {
        transform: scale(1);
        filter: drop-shadow(0 0 4px rgba(255, 69, 0, 0.6));
    }
    50% {
        transform: scale(1.1);
        filter: drop-shadow(0 0 8px rgba(255, 69, 0, 0.8));
    }
}

/* 高伤害文本样式 */
.high-damage-text {
    color: #ff4500;
    font-weight: bold;
    text-shadow: 0 0 4px rgba(255, 69, 0, 0.4);
}

.rating-change {
    font-weight: bold;
    font-size: 1.1rem;
}

.rating-positive {
    color: #28a745;
}

.rating-negative {
    color: #dc3545;
}

/* 比赛详情样式 */
.match-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
    font-size: 0.85rem;
    color: #666;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 6px;
}

.detail-icon {
    width: 14px;
    height: 14px;
    opacity: 0.6;
}

.match-id-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border-bottom: 1px solid transparent;
}

.match-id-link:hover {
    color: #5a67d8;
    border-bottom-color: #5a67d8;
    text-decoration: none;
}

.match-id-link:active {
    color: #4c51bf;
}

/* 无结果样式 */
.no-results {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-results h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.no-results p {
    font-size: 1rem;
    opacity: 0.8;
}

/* 流式数据监控面板样式 */
#streaming-status {
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
    border: 2px solid #4CAF50;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

#streaming-status h3 {
    margin: 0;
    color: #2196F3;
    font-weight: 600;
}

#connection-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #4CAF50;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search-form {
        flex-direction: column;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .match-details {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }
} 

/* 响应式设计 */
@media (max-width: 1400px) {
    .container {
        max-width: 98vw; /* 在较小屏幕上使用更多空间 */
        min-width: 1000px;
    }
}

@media (max-width: 1200px) {
    .container {
        max-width: 99vw;
        min-width: 900px;
    }
    
    body {
        padding: 5px; /* 在较小屏幕上进一步减少padding */
    }
}

@media (max-width: 1000px) {
    .container {
        max-width: 100vw;
        min-width: 800px;
        border-radius: 10px; /* 减小圆角 */
    }
    
    .header {
        padding: 15px 20px;
    }
    
    .header h1 {
        font-size: 1.8rem;
    }
    
    .header p {
        font-size: 0.9rem;
    }
}

/* 超宽屏幕优化 */
@media (min-width: 1600px) {
    .container {
        max-width: 1600px; /* 在超宽屏幕上限制最大宽度 */
    }
} 