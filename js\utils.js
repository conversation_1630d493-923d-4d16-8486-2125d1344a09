/**
 * 工具函数模块
 * 包含时间格式化、数据验证、通用工具函数
 */

/**
 * 格式化时间 - 直接显示日期
 * @param {string|number} timestamp - 时间戳
 * @returns {string} 格式化后的时间字符串
 */
function formatTime(timestamp) {
    if (!timestamp) return '--';
    
    let date;
    
    // 处理不同类型的日期格式
    if (typeof timestamp === 'string') {
        // ISO 8601 字符串格式 (如 "2025-08-02T00:00:00.000Z")
        date = new Date(timestamp);
    } else if (typeof timestamp === 'number') {
        // 数字时间戳
        if (timestamp < 1000000000000) {
            // 秒级时间戳，转换为毫秒
            date = new Date(timestamp * 1000);
        } else {
            // 毫秒级时间戳
            date = new Date(timestamp);
        }
    } else {
        console.warn('不支持的时间格式:', timestamp);
        return '--';
    }
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
        console.warn('无效的时间值:', timestamp);
        return '--';
    }
    
    // 使用配置的时区格式化
    return date.toLocaleString('zh-CN', TIME_CONFIG.DATE_FORMAT);
}

/**
 * 获取完整时间字符串（用于悬停显示）
 * @param {string|number} timestamp - 时间戳
 * @returns {string} 完整的时间字符串
 */
function getFullTimeString(timestamp) {
    if (!timestamp) return '时间未知';
    
    let date;
    
    // 处理不同类型的日期格式
    if (typeof timestamp === 'string') {
        // ISO 8601 字符串格式 (如 "2025-08-02T00:00:00.000Z")
        date = new Date(timestamp);
    } else if (typeof timestamp === 'number') {
        // 数字时间戳
        if (timestamp < 1000000000000) {
            // 秒级时间戳，转换为毫秒
            date = new Date(timestamp * 1000);
        } else {
            // 毫秒级时间戳
            date = new Date(timestamp);
        }
    } else {
        return '时间格式错误';
    }
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
        return '时间格式错误';
    }
    
    // 直接使用 Asia/Shanghai 时区格式化
    return date.toLocaleString('zh-CN', TIME_CONFIG.FULL_DATE_FORMAT);
}

/**
 * 地图名称映射表（按ID顺序）
 */
const MAP_NAMES = [
    '', '黑海岸', '半岛', '水坝', '寒水港',
    '蜿蜒之河', '空军基地', '石油热潮', '死亡公路', '死亡中心',
    '叶尔加瓦', '冰雪城堡', '苏瓦乌基走廊', '克莱佩达裂谷', '鲁达森林',
    '风啸湾', '切尔尼亚霍夫斯克', '核电站', '加里宁格勒',
    '', '', '', '加里宁格勒' // ID 1为空，ID 3为半岛，ID 20-22为空，ID 23为加里宁格勒
];

/**
 * 根据地图ID获取地图名称
 * @param {number} mapId - 地图ID
 * @returns {string} 地图名称
 */
function getMapName(mapId) {
    if (!mapId || mapId <= 0) return '未知地图';
    if (mapId <= MAP_NAMES.length) {
        return MAP_NAMES[mapId - 1]; // 数组索引从0开始，地图ID从1开始
    }
    return `地图${mapId}`; // 如果ID超出范围，回退到原来的格式
}

/**
 * 格式化时长 - 简洁版本
 * @param {number} seconds - 秒数
 * @returns {string} 格式化后的时长字符串
 */
function formatDuration(seconds) {
    if (!seconds || seconds <= 0) return '--';
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) {
        return `${minutes}分`;
    } else {
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        return remainingMinutes > 0 ? `${hours}h${remainingMinutes}m` : `${hours}h`;
    }
}

/**
 * 验证 Steam ID 格式
 * @param {string} steamId - Steam ID
 * @returns {boolean} 是否为有效的 Steam ID
 */
function validateSteamId(steamId) {
    return /^\d{17}$/.test(steamId);
}

/**
 * 验证玩家统计数据
 * @param {object} stats - 玩家统计数据
 * @returns {boolean} 是否为有效的统计数据
 */
function validatePlayerStats(stats) {
    return stats && 
           typeof stats === 'object' && 
           (stats.winsCount !== undefined || stats.losesCount !== undefined);
}

/**
 * 验证比赛数据
 * @param {object} match - 比赛数据
 * @returns {boolean} 是否为有效的比赛数据
 */
function validateMatchData(match) {
    // 简化验证逻辑 - 只要有任何一种有效格式就通过
    if (!match) {
        console.log(`❌ 比赛数据为空`);
        return false;
    }
    
    // 检查是否有任何有效的比赛数据格式
    const hasOldFormat = match.elo !== undefined && match.elo !== null && match.date !== undefined && match.date !== null;
    const hasNewFormat = match.fightId !== undefined && match.fightId !== null && match.EndTime !== undefined && match.EndTime !== null;
    const hasNewStreamingFormat = match.data && match.data.fightId !== undefined && match.data.fightId !== null && match.data.EndTime !== undefined && match.data.EndTime !== null;
    
    const isValid = hasOldFormat || hasNewFormat || hasNewStreamingFormat;
    
    // 添加详细的调试信息
    console.log(`🔍 比赛数据验证详情:`, {
        hasMatch: !!match,
        hasOldFormat: hasOldFormat,
        hasNewFormat: hasNewFormat,
        hasNewStreamingFormat: hasNewStreamingFormat,
        hasFightId: match.fightId !== undefined && match.fightId !== null,
        hasEndTime: match.EndTime !== undefined && match.EndTime !== null,
        hasDataFightId: match.data ? (match.data.fightId !== undefined && match.data.fightId !== null) : false,
        hasDataEndTime: match.data ? (match.data.EndTime !== undefined && match.data.EndTime !== null) : false,
        hasElo: match.elo !== undefined && match.elo !== null,
        hasDate: match.date !== undefined && match.date !== null,
        fightIdValue: match.fightId,
        endTimeValue: match.EndTime,
        fightIdType: typeof match.fightId,
        endTimeType: typeof match.EndTime,
        match: match
    });
    
    // 如果验证失败，但数据看起来是有效的，强制返回true
    if (!isValid && match.fightId && match.EndTime) {
        console.log(`⚠️ 数据看起来有效但验证失败，强制通过验证:`, {
            fightId: match.fightId,
            EndTime: match.EndTime,
            match: match
        });
        return true;
    }
    
    if (!isValid) {
        console.log(`❌ 比赛数据验证失败:`, {
            hasMatch: !!match,
            hasOldFormat: hasOldFormat,
            hasNewFormat: hasNewFormat,
            hasNewStreamingFormat: hasNewStreamingFormat,
            hasFightId: match.fightId !== undefined && match.fightId !== null,
            hasEndTime: match.EndTime !== undefined && match.EndTime !== null,
            hasDataFightId: match.data ? (match.data.fightId !== undefined && match.data.fightId !== null) : false,
            hasDataEndTime: match.data ? (match.data.EndTime !== undefined && match.data.EndTime !== null) : false,
            hasElo: match.elo !== undefined && match.elo !== null,
            hasDate: match.date !== undefined && match.date !== null,
            match: match
        });
    }
    
    return isValid;
}

/**
 * 处理玩家统计数据
 * @param {object} rawStats - 原始统计数据
 * @returns {object} 处理后的统计数据
 */
function processPlayerStats(rawStats) {
    return {
        name: rawStats.name || '未知玩家',
        winsCount: parseInt(rawStats.winsCount) || 0,
        losesCount: parseInt(rawStats.losesCount) || 0,
        fightsCount: parseInt(rawStats.fightsCount) || 0,
        kdRatio: parseFloat(rawStats.kdRatio) || 0
    };
}

/**
 * 处理比赛数据映射
 * @param {object} rawMatch - 原始比赛数据
 * @returns {object} 处理后的比赛数据
 */
function processMatchData(rawMatch) {
    // 判断是流式新格式
    const isNewStreamingFormat = rawMatch.data && rawMatch.data.fightId !== undefined;
    
    if (isNewStreamingFormat) {
        // 流式新格式数据处理
        const matchData = rawMatch.data;
        console.log(`🔍 处理流式新格式比赛数据: fightId=${matchData.fightId}, EndTime=${matchData.EndTime}`);
        
        // 从流式新格式中提取必要信息
        const matchId = matchData.fightId || 0;
        const endTime = matchData.EndTime || 0;
        const playTime = matchData.TotalPlayTimeInSec || 0;
        const mapId = matchData.MapId || 0;
        
        // 根据分数变化判断胜负
        const ratingChange = matchData.ratingChange || 0;
        const isWin = ratingChange > 0;
        
        // 计算最高玩家伤害
        const damageInfo = calculateMaxPlayerDamage(matchData);
        
        console.log(`🔍 流式新格式胜负判断: ratingChange=${ratingChange}, isWin=${isWin}, maxDamage=${damageInfo.maxDamage}, hasHighDamage=${damageInfo.hasHighDamage}`);
        
        return {
            match_id: matchId,
            date: endTime,
            rating: ratingChange,
            ratingChange: ratingChange,
            map: getMapName(mapId),
            duration: playTime,
            result: isWin ? "win" : "loss",
            totalDamage: damageInfo.maxDamage,
            hasHighDamage: damageInfo.hasHighDamage
        };
    }
    
    // 判断是直接新格式
    const isNewFormat = rawMatch.fightId !== undefined && rawMatch.EndTime !== undefined;
    
    if (isNewFormat) {
        // 直接新格式数据处理
        console.log(`🔍 处理直接新格式比赛数据: fightId=${rawMatch.fightId}, EndTime=${rawMatch.EndTime}`);
        
        // 从新格式中提取必要信息
        const matchId = rawMatch.fightId || 0;
        const endTime = rawMatch.EndTime || 0;
        const playTime = rawMatch.TotalPlayTimeInSec || 0;
        const mapId = rawMatch.MapId || 0;
        
        // 根据分数变化判断胜负
        const ratingChange = rawMatch.ratingChange || 0;
        const isWin = ratingChange > 0;
        
        // 计算最高玩家伤害
        const damageInfo = calculateMaxPlayerDamage(rawMatch);
        
        console.log(`🔍 直接新格式胜负判断: ratingChange=${ratingChange}, isWin=${isWin}, maxDamage=${damageInfo.maxDamage}, hasHighDamage=${damageInfo.hasHighDamage}`);
        
        return {
            match_id: matchId,
            date: endTime,
            rating: ratingChange,
            ratingChange: ratingChange,
            map: getMapName(mapId),
            duration: playTime,
            result: isWin ? "win" : "loss",
            totalDamage: damageInfo.maxDamage,
            hasHighDamage: damageInfo.hasHighDamage
        };
    } else {
        // 旧格式数据处理
        console.log(`🔍 处理旧格式比赛数据: match_id=${rawMatch.match_id}, elo=${rawMatch.elo}`);
        
        // 调试时间戳
        if (rawMatch.date) {
            console.log(`🔍 原始时间戳: ${rawMatch.date}, 类型: ${typeof rawMatch.date}`);
            
            let testDate;
            if (typeof rawMatch.date === 'string') {
                testDate = new Date(rawMatch.date);
            } else if (typeof rawMatch.date === 'number') {
                testDate = new Date(rawMatch.date * 1000);
            }
            
            if (testDate) {
                console.log(`🔍 转换后日期: ${testDate.toISOString()}, 有效: ${!isNaN(testDate.getTime())}`);
            }
        }
        
        // 根据分数变化判断胜负（旧格式）
        const ratingChange = rawMatch.elo || 0;
        const isWin = ratingChange > 0;
        
        console.log(`🔍 旧格式胜负判断: elo=${rawMatch.elo}, ratingChange=${ratingChange}, isWin=${isWin}`);
        
        return {
            match_id: rawMatch.match_id || 0,
            date: rawMatch.date,
            rating: ratingChange,
            ratingChange: ratingChange,
            map: rawMatch.map || "未知地图",
            duration: rawMatch.duration || 0,
            result: isWin ? "win" : "loss",
            totalDamage: 0, // 旧格式没有伤害数据
            hasHighDamage: false
        };
    }
}

/**
 * 计算比赛最高玩家伤害
 * @param {Object} matchData - 比赛数据
 * @returns {Object} 包含最高伤害值和是否超过1800的对象
 */
function calculateMaxPlayerDamage(matchData) {
    if (!matchData || !matchData.Data) {
        return { maxDamage: 0, hasHighDamage: false };
    }
    
    let maxDamage = 0;
    
    // 遍历所有玩家数据，找出最高伤害
    for (const playerId in matchData.Data) {
        const playerData = matchData.Data[playerId];
        if (playerData && playerData.DamageDealt !== undefined) {
            const damage = playerData.DamageDealt;
            if (damage > maxDamage) {
                maxDamage = damage;
            }
        }
    }
    
    return {
        maxDamage: Math.round(maxDamage),
        hasHighDamage: maxDamage > 2000
    };
}

/**
 * JSON修复辅助函数
 * @param {string} jsonStr - JSON字符串
 * @returns {string|null} 修复后的JSON字符串或null
 */
function tryFixJsonString(jsonStr) {
    const trimmed = jsonStr.trim();
    
    // 如果字符串为空，无法修复
    if (!trimmed) {
        return null;
    }
    
    // 如果字符串以冒号或逗号开头，说明是JSON对象的中间部分
    if (trimmed.startsWith(':') || trimmed.startsWith(',')) {
        return null; // 无法修复，跳过
    }
    
    // 检查是否是完整的JSON对象
    if (trimmed.startsWith('{') && trimmed.endsWith('}')) {
        // 检查大括号是否匹配
        const openBraces = (trimmed.match(/\{/g) || []).length;
        const closeBraces = (trimmed.match(/\}/g) || []).length;
        if (openBraces === closeBraces) {
            return trimmed; // 已经是完整的JSON
        }
    }
    
    // 如果缺少开始的大括号
    if (!trimmed.startsWith('{') && trimmed.includes('}')) {
        return '{' + trimmed;
    }
    
    // 如果缺少结束的大括号
    if (trimmed.startsWith('{') && !trimmed.endsWith('}')) {
        const openBraces = (trimmed.match(/\{/g) || []).length;
        const closeBraces = (trimmed.match(/\}/g) || []).length;
        const missingBraces = openBraces - closeBraces;
        
        if (missingBraces > 0 && missingBraces <= 3) {
            return trimmed + '}'.repeat(missingBraces);
        }
    }
    
    // 尝试修复常见的JSON格式问题
    // 1. 移除末尾的逗号
    if (trimmed.endsWith(',')) {
        const withoutComma = trimmed.slice(0, -1);
        if (withoutComma.endsWith('}')) {
            return withoutComma;
        }
    }
    
    // 2. 修复未闭合的字符串
    const quoteCount = (trimmed.match(/"/g) || []).length;
    if (quoteCount % 2 !== 0) {
        // 奇数个引号，尝试添加结束引号
        const lastQuoteIndex = trimmed.lastIndexOf('"');
        if (lastQuoteIndex > 0) {
            const beforeLastQuote = trimmed.substring(0, lastQuoteIndex + 1);
            const afterLastQuote = trimmed.substring(lastQuoteIndex + 1);
            // 在最后一个引号后添加结束引号和大括号
            return beforeLastQuote + '"' + afterLastQuote + '}';
        }
    }
    
    return null; // 无法修复
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间（毫秒）
 * @returns {Function} 节流后的函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
} 