# 比赛记录查询与战术分析系统

一个现代化的比赛记录查询和战术分析系统，支持流式数据处理、实时监控和深度战术分析。

## 📁 文件结构

```
/
├── index.html                    # 主HTML文件（包含单位使用分析）
├── tactical-profile.html         # 战术档案分析页面
├── unit-analysis-demo.html       # 单位分析演示页面
├── user-list.html               # 用户列表页面
├── styles/
│   ├── main.css                 # 主样式文件
│   └── user-list.css            # 用户列表样式
├── js/
│   ├── config.js                # 配置和常量
│   ├── utils.js                 # 工具函数
│   ├── api.js                   # API服务和数据获取
│   ├── ui.js                    # UI渲染和界面更新
│   ├── events.js                # 事件处理和用户交互
│   ├── app.js                   # 主应用逻辑和初始化
│   ├── unit-analysis.js         # 单位分析核心逻辑
│   ├── unit-mapping.js          # 单位映射配置
│   ├── user-list-api.js         # 用户列表API
│   ├── user-list-app.js         # 用户列表应用逻辑
│   └── user-list-ui.js          # 用户列表UI
├── start-all.bat                # Windows启动脚本
├── start-server.py              # 本地服务器
├── cors-proxy.py                # CORS代理服务器
├── README.md                    # 项目说明
├── README-Unit-Analysis.md      # 单位分析功能说明
├── README-Data-Analysis.md      # 数据分析功能说明
└── README-Tactical-Profile.md   # 战术档案分析说明
```

## 🚀 快速开始

### Windows 用户
1. 双击 `start-all.bat` 启动完整环境
2. 访问 http://localhost:8000/index.html

### 其他系统
1. 运行 `python start-server.py` 启动本地服务器
2. 运行 `python cors-proxy.py` 启动CORS代理
3. 访问 http://localhost:8000/index.html

## 📋 功能特性

### 核心功能
- **流式数据处理**: 实时接收和显示比赛数据
- **智能错误处理**: 多级错误恢复机制
- **响应式设计**: 适配各种屏幕尺寸
- **模块化架构**: 清晰的代码组织结构
- **开发模式**: 内置调试工具和性能监控

### 分析功能
- **单位使用分析**: 详细分析比赛中的单位使用情况
- **战术档案分析**: 为每个玩家建立战术偏好档案
- **伤害来源分析**: 识别玩家依赖的主要伤害来源单位
- **团队协作分析**: 分析团队中的战术配合模式
- **历史趋势分析**: 追踪玩家战术随时间的变化

## 🔧 技术栈

- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **样式**: CSS Grid, Flexbox, CSS动画
- **数据处理**: 流式API, JSON解析, 数据验证
- **网络**: Fetch API, CORS代理, 错误重试

## 📊 模块说明

### 核心模块
#### config.js
- API端点配置
- 数据处理参数
- 错误消息定义
- 时间格式化配置

#### utils.js
- 时间格式化函数
- 数据验证函数
- JSON修复工具
- 通用工具函数

#### api.js
- API请求封装
- 流式数据处理
- 错误处理和重试
- 代理服务器支持

#### ui.js
- 页面渲染逻辑
- DOM操作封装
- 加载状态管理
- 进度条更新

#### events.js
- 表单提交处理
- 点击事件管理
- URL参数处理
- 页面初始化

#### app.js
- 应用状态管理
- 模块依赖检查
- 性能监控
- 开发模式支持

### 分析模块
#### unit-analysis.js
- 单位使用分析核心逻辑
- 玩家数据统计
- 单位效率计算
- 团队协作分析

#### unit-mapping.js
- 单位ID到名称映射
- 单位分类配置
- 分类颜色定义
- 单位详情链接生成

#### user-list-api.js
- 用户列表API封装
- 批量数据处理
- 用户信息获取

#### user-list-app.js
- 用户列表应用逻辑
- 数据过滤和排序
- 分页处理

#### user-list-ui.js
- 用户列表界面渲染
- 表格组件
- 搜索和筛选UI

## 🎯 使用示例

### 基本查询
1. 输入17位Steam ID
2. 点击查询按钮
3. 查看玩家统计和比赛记录

### 单位使用分析
1. 访问 `http://localhost:8000/index.html`
2. 切换到"单位使用分析"标签页
3. 输入Steam ID获取比赛数据
4. 选择特定比赛进行分析
5. 查看详细的单位使用统计

### 战术档案分析
1. 访问 `http://localhost:8000/tactical-profile.html`
2. 输入Steam ID和分析场数
3. 点击"获取战术档案"
4. 查看每个玩家的详细战术档案
5. 分析主要伤害来源单位

### URL参数查询
```
http://localhost:8000/index.html?steamid=76561198012345678
```

### 开发调试
在本地开发模式下，可以使用控制台的调试工具：
```javascript
// 查看应用状态
AppDebug.state()

// 重置应用状态
AppDebug.reset()

// 测试渲染
AppDebug.test.renderTest()
```

## 🔍 故障排除

### CORS错误
- 确保CORS代理服务器正在运行
- 检查浏览器控制台错误信息
- 尝试使用浏览器CORS扩展

### 数据加载失败
- 检查网络连接
- 验证Steam ID格式
- 查看API服务器状态

### 页面显示异常
- 刷新页面重试
- 检查JavaScript控制台错误
- 确认所有文件路径正确

## 📝 开发说明

### 代码规范
- 使用ES6+语法
- 函数式编程风格
- 详细的JSDoc注释
- 模块化设计

### 性能优化
- 流式数据处理
- 增量UI更新
- 防抖和节流
- 内存管理

### 错误处理
- 全局错误捕获
- 网络请求重试
- 用户友好提示
- 降级处理机制

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

## 📞 支持

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件
- 在线讨论

---

**注意**: 请确保在使用前正确配置API端点和代理服务器。 