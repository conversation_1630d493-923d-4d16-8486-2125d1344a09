/**
 * 用户列表主应用模块
 * 负责应用的初始化和事件处理
 */

// 应用状态
let appState = {
    users: [],
    sortBy: 'lastOnline',
    sortAscending: false,
    filterText: '',
    statusFilter: 'all',
    isLoading: false
};

/**
 * 初始化用户列表应用
 */
function initializeUserListApp() {
    console.log('🚀 初始化用户列表应用');
    
    // 初始化 DOM 元素
    window.UserListUI.initializeUserListDOMElements();
    
    // 绑定事件监听器
    bindUserListEventListeners();
    
    // 显示欢迎信息，等待用户点击开始
    showWelcomeMessage();
    
    console.log('✅ 用户列表应用初始化完成');
}

/**
 * 开始数据获取
 */
function startDataFetch() {
    console.log('🚀 用户点击开始获取按钮');
    
    // 隐藏开始按钮，显示刷新按钮
    const startBtn = document.getElementById(window.USER_LIST_DOM_IDS.START_BTN);
    const refreshBtn = document.getElementById(window.USER_LIST_DOM_IDS.REFRESH_BTN);
    
    if (startBtn) {
        startBtn.style.display = 'none';
    }
    if (refreshBtn) {
        refreshBtn.style.display = 'flex';
    }
    
    // 开始加载数据
    loadUserData();
}

/**
 * 显示欢迎信息
 */
function showWelcomeMessage() {
    const loadingElement = document.getElementById(window.USER_LIST_DOM_IDS.LOADING);
    if (loadingElement) {
        loadingElement.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <div style="font-size: 48px; margin-bottom: 20px;">👥</div>
                <h2 style="color: #2196F3; margin-bottom: 15px;">欢迎使用用户列表监控</h2>
                <p style="color: #666; margin-bottom: 30px; font-size: 16px;">
                    点击"开始获取"按钮开始监控 ${window.USER_LIST_CONFIG.USERS.length} 个用户的在线状态
                </p>
                <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="margin: 0 0 10px 0; color: #333;">功能说明：</h3>
                    <ul style="text-align: left; margin: 0; padding-left: 20px; color: #666;">
                        <li>智能分批处理，避免服务器压力</li>
                        <li>实时显示获取进度</li>
                        <li>支持排序和过滤功能</li>
                        <li>自动缓存数据，提高效率</li>
                    </ul>
                </div>
            </div>
        `;
        loadingElement.style.display = 'block';
    }
}

/**
 * 绑定事件监听器
 */
function bindUserListEventListeners() {
    const elements = window.UserListUI;
    
    // 开始按钮
    const startBtn = document.getElementById(window.USER_LIST_DOM_IDS.START_BTN);
    if (startBtn) {
        startBtn.addEventListener('click', () => {
            startDataFetch();
        });
    }
    
    // 刷新按钮
    const refreshBtn = document.getElementById(window.USER_LIST_DOM_IDS.REFRESH_BTN);
    if (refreshBtn) {
        refreshBtn.addEventListener('click', () => {
            loadUserData(true); // 强制刷新
        });
    }
    
    // 排序选择
    const sortSelect = document.getElementById(window.USER_LIST_DOM_IDS.SORT_SELECT);
    if (sortSelect) {
        sortSelect.addEventListener('change', (e) => {
            appState.sortBy = e.target.value;
            updateUserDisplay();
        });
    }
    
    // 排序顺序按钮
    const sortOrderBtn = document.getElementById(window.USER_LIST_DOM_IDS.SORT_ORDER_BTN);
    if (sortOrderBtn) {
        sortOrderBtn.addEventListener('click', () => {
            appState.sortAscending = !appState.sortAscending;
            window.UserListUI.updateSortOrderButton(appState.sortAscending);
            updateUserDisplay();
        });
    }
    
    // 过滤输入
    const filterInput = document.getElementById(window.USER_LIST_DOM_IDS.FILTER_INPUT);
    if (filterInput) {
        filterInput.addEventListener('input', (e) => {
            appState.filterText = e.target.value;
            updateUserDisplay();
        });
    }
    
    // 状态过滤
    const statusFilter = document.getElementById(window.USER_LIST_DOM_IDS.STATUS_FILTER);
    if (statusFilter) {
        statusFilter.addEventListener('change', (e) => {
            appState.statusFilter = e.target.value;
            updateUserDisplay();
        });
    }
    
    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
        // F5 或 Ctrl+R 刷新
        if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
            e.preventDefault();
            loadUserData(true);
        }
        
        // Ctrl+F 聚焦搜索框
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            if (filterInput) {
                filterInput.focus();
                filterInput.select();
            }
        }
    });
    
    // 页面卸载时的处理
    window.addEventListener('beforeunload', handleUserListPageUnload);
    
    // 全局错误处理
    window.addEventListener('error', handleUserListGlobalError);
    
    console.log('✅ 事件监听器已绑定');
}

/**
 * 加载用户数据
 * @param {boolean} forceRefresh - 是否强制刷新
 */
async function loadUserData(forceRefresh = false) {
    if (appState.isLoading) {
        console.log('⏳ 正在加载中，跳过重复请求');
        return;
    }
    
    appState.isLoading = true;
    window.UserListUI.setRefreshButtonState(true);
    window.UserListUI.showUserListLoading();
    
    try {
        console.log(`🔄 开始加载用户数据 (强制刷新: ${forceRefresh})`);
        
        const users = window.USER_LIST_CONFIG.USERS;
        
        // 获取用户在线时间
        const usersWithOnlineTime = await window.UserListAPI.getUsersLastOnlineTime(
            users,
            (current, total) => {
                window.UserListUI.updateUserListProgress(current, total);
            },
            forceRefresh
        );
        
        appState.users = usersWithOnlineTime;
        
        // 更新显示
        updateUserDisplay();
        
        console.log(`✅ 用户数据加载完成，共 ${appState.users.length} 个用户`);
        
    } catch (error) {
        console.error('❌ 加载用户数据失败:', error);
        window.UserListUI.showUserListError('加载用户数据失败，请稍后重试');
    } finally {
        appState.isLoading = false;
        window.UserListUI.setRefreshButtonState(false);
        window.UserListUI.hideUserListLoading();
    }
}

/**
 * 更新用户显示
 */
function updateUserDisplay() {
    if (appState.users.length === 0) {
        return;
    }
    
    // 排序用户
    const sortedUsers = window.UserListUI.sortUsers(
        appState.users,
        appState.sortBy,
        appState.sortAscending
    );
    
    // 渲染统计信息
    window.UserListUI.renderUserStats(sortedUsers);
    
    // 渲染用户列表
    window.UserListUI.renderUserList(
        sortedUsers,
        appState.filterText,
        appState.statusFilter
    );
}

/**
 * 处理页面卸载
 */
function handleUserListPageUnload() {
    console.log('📄 用户列表页面即将卸载');
    // 可以在这里保存状态或清理资源
}

/**
 * 全局错误处理
 * @param {ErrorEvent} event - 错误事件
 */
function handleUserListGlobalError(event) {
    console.error('🚨 全局错误:', event.error);
    
    // 显示用户友好的错误信息
    const errorMessage = event.error?.message || '发生未知错误';
    window.UserListUI.showUserListError(`应用错误: ${errorMessage}`);
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', initializeUserListApp);

// 导出函数到全局作用域
window.UserListApp = {
    initializeUserListApp,
    bindUserListEventListeners,
    loadUserData,
    updateUserDisplay,
    handleUserListPageUnload,
    handleUserListGlobalError,
    appState
};

console.log('✅ 用户列表应用模块已加载');