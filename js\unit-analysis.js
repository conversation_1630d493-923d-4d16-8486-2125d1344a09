/**
 * 单位使用分析模块
 * 统计每个人每种单位的使用情况
 */

console.log('🎯 单位使用分析模块已加载！');

/**
 * 分析单位使用情况
 * @param {Object} matchData - 比赛数据
 * @returns {Object} 单位使用统计
 */
function analyzeUnitUsage(matchData) {
    const data = matchData.data || matchData;
    const players = data.Data || {};
    
    const unitAnalysis = {
        totalUnits: 0,
        uniqueUnits: new Set(),
        unitUsage: {},
        playerUnitDetails: [],
        unitCategories: {
            infantry: 0,
            vehicle: 0,
            aircraft: 0,
            artillery: 0,
            support: 0,
            unknown: 0
        },
        mostUsedUnits: [],
        unitEfficiency: {},
        playerUnitStats: {}
    };
    
    // 分析每个玩家的单位使用情况
    for (const [playerId, playerData] of Object.entries(players)) {
        const unitData = playerData.UnitData || {};
        const transferredUnits = playerData.TransferredUnits || {};
        
        const playerUnitInfo = {
            playerId: playerId,
            playerName: playerData.Name || playerId,
            teamId: playerData.TeamId || 'unknown',
            units: [],
            totalUnits: 0,
            unitTypes: new Set(),
            unitEfficiency: {}
        };
        
        // 分析主要单位数据
        for (const [unitId, unitInfo] of Object.entries(unitData)) {
            const unitId_num = parseInt(unitInfo.Id);
            const unitName = getUnitName(unitId_num);
            const unitCategory = categorizeUnit(unitId_num);
            
            // 统计单位使用
            if (!unitAnalysis.unitUsage[unitId_num]) {
                unitAnalysis.unitUsage[unitId_num] = {
                    id: unitId_num,
                    name: unitName,
                    category: unitCategory,
                    usageCount: 0,
                    totalDamage: 0,
                    totalKills: 0,
                    totalDeaths: 0,
                    players: []
                };
            }
            
            unitAnalysis.unitUsage[unitId_num].usageCount++;
            unitAnalysis.unitUsage[unitId_num].totalDamage += unitInfo.TotalDamageDealt || 0;
            unitAnalysis.unitUsage[unitId_num].totalKills += unitInfo.KilledCount || 0;
            unitAnalysis.unitUsage[unitId_num].players.push(playerId);
            
            // 更新分类统计
            unitAnalysis.unitCategories[unitCategory]++;
            unitAnalysis.uniqueUnits.add(unitId_num);
            unitAnalysis.totalUnits++;
            
            // 调试日志：检查 TotalDamageReceived 原始值
            console.log(`🔍 单位 ${unitId_num} (${unitName}) - 原始 TotalDamageReceived:`, unitInfo.TotalDamageReceived);
            
            // 玩家单位详情
            playerUnitInfo.units.push({
                unitId: unitId_num,
                unitName: unitName,
                category: unitCategory,
                damage: unitInfo.TotalDamageDealt || 0,
                kills: unitInfo.KilledCount || 0,
                damageReceived: unitInfo.TotalDamageReceived || 0,
                wasRefunded: unitInfo.WasRefunded || false,
                options: unitInfo.OptionIds || []
            });
            
            // 调试日志：检查处理后的 damageReceived 值
            console.log(`🔍 单位 ${unitId_num} (${unitName}) - 处理后 damageReceived:`, unitInfo.TotalDamageReceived || 0);
            
            playerUnitInfo.totalUnits++;
            playerUnitInfo.unitTypes.add(unitCategory);
        }
        
        // 分析转移的单位
        for (const [unitId, unitInfo] of Object.entries(transferredUnits)) {
            const unitId_num = parseInt(unitInfo.Id);
            const unitName = getUnitName(unitId_num);
            const unitCategory = categorizeUnit(unitId_num);
            
            playerUnitInfo.units.push({
                unitId: unitId_num,
                unitName: unitName,
                category: unitCategory,
                damage: 0,
                kills: 0,
                damageReceived: 0,
                wasRefunded: false,
                options: unitInfo.OptionIds || [],
                transferred: true
            });
            
            playerUnitInfo.totalUnits++;
            playerUnitInfo.unitTypes.add(unitCategory);
        }
        
        // 计算玩家单位效率
        playerUnitInfo.unitEfficiency = calculatePlayerUnitEfficiency(playerUnitInfo.units);
        
        unitAnalysis.playerUnitDetails.push(playerUnitInfo);
        
        // 生成玩家单位统计
        unitAnalysis.playerUnitStats[playerId] = generatePlayerUnitStats(playerUnitInfo);
    }
    
    // 生成最常用单位列表
    unitAnalysis.mostUsedUnits = Object.values(unitAnalysis.unitUsage)
        .sort((a, b) => b.usageCount - a.usageCount)
        .slice(0, 10);
    
    // 计算单位效率统计
    unitAnalysis.unitEfficiency = calculateOverallUnitEfficiency(unitAnalysis.unitUsage);
    
    return unitAnalysis;
}

/**
 * 获取单位名称
 */
function getUnitName(unitId) {
    // 这里可以添加单位ID到名称的映射
    // 暂时返回单位ID
    return `单位${unitId}`;
}

/**
 * 单位分类
 */
function categorizeUnit(unitId) {
    // 基于单位ID进行分类
    // 这些分类需要根据实际游戏单位进行调整
    if (unitId >= 1 && unitId <= 50) return 'infantry';      // 步兵
    if (unitId >= 51 && unitId <= 150) return 'vehicle';     // 车辆
    if (unitId >= 151 && unitId <= 250) return 'aircraft';   // 飞机
    if (unitId >= 251 && unitId <= 350) return 'artillery';  // 炮兵
    if (unitId >= 351 && unitId <= 450) return 'support';    // 支援
    return 'unknown';
}

/**
 * 计算玩家单位效率
 */
function calculatePlayerUnitEfficiency(units) {
    const totalDamage = units.reduce((sum, unit) => sum + unit.damage, 0);
    const totalKills = units.reduce((sum, unit) => sum + unit.kills, 0);
    const totalDamageReceived = units.reduce((sum, unit) => sum + unit.damageReceived, 0);
    const activeUnits = units.filter(unit => !unit.wasRefunded && !unit.transferred);
    
    return {
        totalDamage: totalDamage,
        totalKills: totalKills,
        totalDamageReceived: totalDamageReceived,
        activeUnits: activeUnits.length,
        damagePerUnit: activeUnits.length > 0 ? (totalDamage / activeUnits.length).toFixed(1) : 0,
        killsPerUnit: activeUnits.length > 0 ? (totalKills / activeUnits.length).toFixed(1) : 0,
        efficiency: activeUnits.length > 0 ? ((totalDamage + totalKills * 100) / activeUnits.length).toFixed(0) : 0
    };
}

/**
 * 生成玩家单位统计
 */
function generatePlayerUnitStats(playerUnitInfo) {
    const stats = {
        playerName: playerUnitInfo.playerName,
        totalUnits: playerUnitInfo.totalUnits,
        unitTypes: Array.from(playerUnitInfo.unitTypes),
        unitBreakdown: {},
        categoryStats: {
            infantry: { count: 0, damage: 0, kills: 0 },
            vehicle: { count: 0, damage: 0, kills: 0 },
            aircraft: { count: 0, damage: 0, kills: 0 },
            artillery: { count: 0, damage: 0, kills: 0 },
            support: { count: 0, damage: 0, kills: 0 },
            unknown: { count: 0, damage: 0, kills: 0 }
        },
        efficiency: playerUnitInfo.unitEfficiency
    };
    
    // 按单位类型统计
    for (const unit of playerUnitInfo.units) {
        const category = unit.category;
        
        // 更新分类统计
        stats.categoryStats[category].count++;
        stats.categoryStats[category].damage += unit.damage;
        stats.categoryStats[category].kills += unit.kills;
        
        // 更新单位详细统计
        if (!stats.unitBreakdown[unit.unitId]) {
            stats.unitBreakdown[unit.unitId] = {
                name: unit.unitName,
                category: unit.category,
                count: 0,
                damage: 0,
                kills: 0,
                damageReceived: 0,
                options: unit.options || []
            };
        }
        
        stats.unitBreakdown[unit.unitId].count++;
        stats.unitBreakdown[unit.unitId].damage += unit.damage;
        stats.unitBreakdown[unit.unitId].kills += unit.kills;
        stats.unitBreakdown[unit.unitId].damageReceived += unit.damageReceived;
    }
    
    return stats;
}

/**
 * 计算整体单位效率
 */
function calculateOverallUnitEfficiency(unitUsage) {
    const efficiency = {
        mostEfficient: [],
        leastEfficient: [],
        averageEfficiency: 0
    };
    
    const unitEfficiencies = Object.values(unitUsage).map(unit => ({
        id: unit.id,
        name: unit.name,
        efficiency: unit.usageCount > 0 ? (unit.totalDamage + unit.totalKills * 100) / unit.usageCount : 0
    }));
    
    // 按效率排序
    unitEfficiencies.sort((a, b) => b.efficiency - a.efficiency);
    
    efficiency.mostEfficient = unitEfficiencies.slice(0, 5);
    efficiency.leastEfficient = unitEfficiencies.slice(-5).reverse();
    efficiency.averageEfficiency = unitEfficiencies.reduce((sum, unit) => sum + unit.efficiency, 0) / unitEfficiencies.length;
    
    return efficiency;
}

/**
 * 格式化单位分析结果
 */
function formatUnitAnalysis(unitAnalysis) {
    return {
        summary: generateUnitSummary(unitAnalysis),
        details: generateUnitDetails(unitAnalysis),
        insights: generateUnitInsights(unitAnalysis)
    };
}

/**
 * 生成单位使用摘要
 */
function generateUnitSummary(unitAnalysis) {
    return {
        totalUnits: unitAnalysis.totalUnits,
        uniqueUnits: unitAnalysis.uniqueUnits.size,
        unitCategories: unitAnalysis.unitCategories,
        mostUsedUnit: unitAnalysis.mostUsedUnits.length > 0 ? unitAnalysis.mostUsedUnits[0] : null,
        averageUnitsPerPlayer: unitAnalysis.totalUnits / unitAnalysis.playerUnitDetails.length
    };
}

/**
 * 生成单位使用详情
 */
function generateUnitDetails(unitAnalysis) {
    return {
        unitUsage: unitAnalysis.unitUsage,
        mostUsedUnits: unitAnalysis.mostUsedUnits,
        unitEfficiency: unitAnalysis.unitEfficiency,
        playerUnitStats: unitAnalysis.playerUnitStats
    };
}

/**
 * 生成单位使用洞察
 */
function generateUnitInsights(unitAnalysis) {
    const insights = [];
    
    // 单位使用多样性
    const avgUnitsPerPlayer = unitAnalysis.totalUnits / unitAnalysis.playerUnitDetails.length;
    if (avgUnitsPerPlayer > 10) {
        insights.push('玩家使用了大量单位，战术选择丰富');
    } else if (avgUnitsPerPlayer < 3) {
        insights.push('单位使用较少，可能是专注型战术');
    }
    
    // 单位类型分布
    const categoryCounts = unitAnalysis.unitCategories;
    const maxCategory = Object.entries(categoryCounts).reduce((a, b) => a[1] > b[1] ? a : b);
    insights.push(`最常用的单位类型是: ${maxCategory[0]} (${maxCategory[1]} 个)`);
    
    // 最常用单位
    if (unitAnalysis.mostUsedUnits.length > 0) {
        const topUnit = unitAnalysis.mostUsedUnits[0];
        insights.push(`最受欢迎的单位: ${topUnit.name} (使用 ${topUnit.usageCount} 次)`);
    }
    
    // 单位效率
    if (unitAnalysis.unitEfficiency.mostEfficient.length > 0) {
        const mostEfficient = unitAnalysis.unitEfficiency.mostEfficient[0];
        insights.push(`最高效单位: ${mostEfficient.name} (效率: ${mostEfficient.efficiency.toFixed(0)})`);
    }
    
    return insights;
}

/**
 * 获取玩家单位使用统计
 */
function getPlayerUnitStats(unitAnalysis, playerId) {
    return unitAnalysis.playerUnitStats[playerId] || null;
}

/**
 * 获取单位使用统计
 */
function getUnitUsageStats(unitAnalysis, unitId) {
    return unitAnalysis.unitUsage[unitId] || null;
}

/**
 * 获取最常用单位
 */
function getMostUsedUnits(unitAnalysis, limit = 10) {
    return unitAnalysis.mostUsedUnits.slice(0, limit);
}

/**
 * 获取最高效单位
 */
function getMostEfficientUnits(unitAnalysis, limit = 5) {
    return unitAnalysis.unitEfficiency.mostEfficient.slice(0, limit);
}

// 导出函数
window.unitAnalysis = {
    analyzeUnitUsage,
    formatUnitAnalysis,
    getPlayerUnitStats,
    getUnitUsageStats,
    getMostUsedUnits,
    getMostEfficientUnits,
    getUnitName,
    categorizeUnit
};

