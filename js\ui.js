/**
 * UI 渲染模块
 * 包含所有页面渲染、DOM操作和加载状态管理
 */

// DOM 元素引用
let domElements = {};

/**
 * 格式化分数变化，显示到小数点后5位
 * @param {number} ratingChange - 分数变化
 * @returns {string} 格式化后的分数变化文本
 */
function formatRatingChange(ratingChange) {
    if (ratingChange === 0) return '0.00000';
    const formatted = Math.abs(ratingChange).toFixed(5);
    return ratingChange >= 0 ? `+${formatted}` : `-${formatted}`;
}

/**
 * 初始化DOM元素引用
 */
function initializeDOMElements() {
    domElements = {
        searchForm: document.getElementById(DOM_IDS.SEARCH_FORM),
        steamIdInput: document.getElementById(DOM_IDS.STEAM_ID_INPUT),
        searchBtn: document.getElementById(DOM_IDS.SEARCH_BTN),
        loading: document.getElementById(DOM_IDS.LOADING),
        error: document.getElementById(DOM_IDS.ERROR),
        results: document.getElementById(DOM_IDS.RESULTS),
        playerInfo: document.getElementById(DOM_IDS.PLAYER_INFO),
        playerName: document.getElementById(DOM_IDS.PLAYER_NAME),
        playerStats: document.getElementById(DOM_IDS.PLAYER_STATS),
        matchCount: document.getElementById(DOM_IDS.MATCH_COUNT),
        matchesList: document.getElementById(DOM_IDS.MATCHES_LIST),
        corsNotice: document.getElementById(DOM_IDS.CORS_NOTICE),
        progressContainer: document.getElementById(DOM_IDS.PROGRESS_CONTAINER),
        progressFill: document.getElementById(DOM_IDS.PROGRESS_FILL),
        progressText: document.getElementById(DOM_IDS.PROGRESS_TEXT)
    };
}

/**
 * 显示错误信息
 * @param {string} message - 错误消息
 */
function showError(message) {
    domElements.error.textContent = message;
    domElements.error.classList.add('show');
    setTimeout(() => {
        domElements.error.classList.remove('show');
    }, 5000);
}

/**
 * 显示 CORS 问题说明
 */
function showCorsNotice() {
    domElements.corsNotice.classList.add('show');
}

/**
 * 隐藏 CORS 问题说明
 */
function hideCorsNotice() {
    domElements.corsNotice.classList.remove('show');
}

/**
 * 显示加载状态
 */
function showLoading() {
    domElements.loading.classList.add('show');
    domElements.results.style.display = 'none';
    domElements.error.classList.remove('show');
    hideCorsNotice();
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
    domElements.loading.classList.remove('show');
    domElements.progressContainer.style.display = 'none';
}

/**
 * 更新进度条
 * @param {number|null} total - 总数
 * @param {number|null} processed - 已处理数
 */
function updateProgress(total, processed) {
    if (total === null || processed === null) {
        domElements.progressContainer.style.display = 'none';
        return;
    }
    
    domElements.progressContainer.style.display = 'block';
    const percentage = total > 0 ? (processed / total) * 100 : 0;
    domElements.progressFill.style.width = `${percentage}%`;
    domElements.progressText.textContent = `处理比赛记录: ${processed}/${total} (${percentage.toFixed(1)}%)`;
}

/**
 * 渲染玩家统计信息
 * @param {object} stats - 玩家统计数据
 */
function renderPlayerStats(stats) {
    if (!validatePlayerStats(stats)) {
        domElements.playerName.textContent = '玩家信息';
        domElements.playerStats.innerHTML = '<p>无法获取玩家统计信息</p>';
        return;
    }

    const processedStats = processPlayerStats(stats);
    domElements.playerName.textContent = processedStats.name;
    
    const totalMatches = processedStats.winsCount + processedStats.losesCount;
    const winRate = totalMatches > 0 ? (processedStats.winsCount / totalMatches * 100).toFixed(1) : 0;
    
    domElements.playerStats.innerHTML = `
        <div class="stat-item">
            <div class="stat-value">${processedStats.winsCount.toLocaleString()}</div>
            <div class="stat-label">胜利次数</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">${processedStats.losesCount.toLocaleString()}</div>
            <div class="stat-label">失败次数</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">${winRate}%</div>
            <div class="stat-label">胜率</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">${totalMatches.toLocaleString()}</div>
            <div class="stat-label">总比赛数</div>
        </div>
    `;
}

/**
 * 增量渲染比赛记录
 * @param {Array} matches - 比赛数据数组
 */
function renderMatchesIncremental(matches) {
    console.log(`🎨 开始增量渲染，输入matches数量: ${matches ? matches.length : 0}`);
    
    if (!matches || matches.length === 0) {
        console.log(`❌ 没有比赛数据可渲染`);
        domElements.matchesList.innerHTML = `
            <div class="no-results">
                <h3>暂无比赛记录</h3>
                <p>该玩家可能还没有比赛记录或 Steam ID 不正确</p>
            </div>
        `;
        domElements.matchCount.textContent = '0 场比赛';
        return;
    }
    
    // 使用改进的数据处理逻辑
    console.log(`🔍 开始过滤和处理比赛数据...`);
    const filteredMatches = matches.filter(validateMatchData);
    console.log(`✅ 过滤后有效比赛数量: ${filteredMatches.length}/${matches.length}`);
    
    const processedMatches = filteredMatches.map(processMatchData);
    console.log(`🔄 处理后比赛数量: ${processedMatches.length}`);
    
    const validMatches = processedMatches
        .sort((a, b) => b.date - a.date) // 按日期倒序排列
        .slice(0, DATA_CONFIG.MAX_MATCHES_DISPLAY); // 限制显示数量
    
    console.log(`📊 最终显示比赛数量: ${validMatches.length}`);
    domElements.matchCount.textContent = `${validMatches.length} 场比赛`;
    
    // 增量更新，只渲染最新的比赛记录
    const currentMatches = validMatches.slice(0, Math.min(validMatches.length, 10));
    
    domElements.matchesList.innerHTML = currentMatches.map(match => {
        const isWin = match.result === "win";
        const ratingChange = match.ratingChange || 0;
        const ratingChangeText = formatRatingChange(ratingChange);
        
        return `
            <div class="match-item">
                <div class="match-header">
                    <div class="match-result ${isWin ? 'result-win' : 'result-loss'}">
                        <div class="result-indicator ${isWin ? 'result-win' : 'result-loss'}"></div>
                        <span class="result-text">${isWin ? MATCH_RESULT_CONFIG.WIN_TEXT : MATCH_RESULT_CONFIG.LOSS_TEXT}</span>
                        ${match.hasHighDamage ? '<span class="high-damage-badge" title="单人伤害超过2000">🔥</span>' : ''}
                    </div>
                    <div class="rating-change ${ratingChange >= 0 ? 'rating-positive' : 'rating-negative'}">
                        ${ratingChangeText}
                    </div>
                </div>
                <div class="match-details">
                    <div class="detail-item">
                        <svg class="detail-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd"></path>
                            <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"></path>
                        </svg>
                        <a href="${EXTERNAL_LINKS.MATCH_DETAILS(match.match_id)}" target="_blank" class="match-id-link" title="点击查看比赛详情">
                            ${match.match_id}
                        </a>
                    </div>
                    <div class="detail-item">
                        <svg class="detail-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                        <span title="${getFullTimeString(match.date)}">${formatTime(match.date)}</span>
                    </div>
                    <div class="detail-item">
                        <svg class="detail-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span>${match.map}</span>
                    </div>
                    <div class="detail-item">
                        <svg class="detail-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                        <span>${formatDuration(match.duration)}</span>
                    </div>
                    ${match.totalDamage > 0 ? `
                    <div class="detail-item">
                        <svg class="detail-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="${match.hasHighDamage ? 'high-damage-text' : ''}" title="${match.hasHighDamage ? '单人伤害超过2000！' : '最高伤害'}">${match.totalDamage}</span>
                    </div>
                    ` : ''}
                </div>
            </div>
        `;
    }).join('');
    
    // 如果还有更多比赛记录，显示提示
    if (validMatches.length > 10) {
        domElements.matchesList.innerHTML += `
            <div class="more-matches-notice">
                <p>显示最新的 10 场比赛，共 ${validMatches.length} 场比赛记录</p>
            </div>
        `;
    }
}

/**
 * 渲染比赛记录 - 完整版本
 * @param {object} matchesData - 比赛记录数据
 */
function renderMatches(matchesData) {
    if (!matchesData || !matchesData.match_stats || matchesData.match_stats.length === 0) {
        domElements.matchesList.innerHTML = `
            <div class="no-results">
                <h3>暂无比赛记录</h3>
                <p>该玩家可能还没有比赛记录或 Steam ID 不正确</p>
            </div>
        `;
        domElements.matchCount.textContent = '0 场比赛';
        return;
    }

    // 使用改进的数据处理逻辑
    const validMatches = matchesData.match_stats
        .filter(validateMatchData)
        .map(processMatchData)
        .filter(match => match !== null) // 过滤掉处理失败的比赛
        .sort((a, b) => b.date - a.date) // 按日期倒序排列
        .slice(0, DATA_CONFIG.MAX_MATCHES_DISPLAY); // 限制显示数量

    if (validMatches.length === 0) {
        domElements.matchesList.innerHTML = `
            <div class="no-results">
                <h3>数据处理失败</h3>
                <p>比赛数据格式不正确或无法解析，请检查数据源</p>
            </div>
        `;
        domElements.matchCount.textContent = '0 场比赛';
        return;
    }

    domElements.matchCount.textContent = `${validMatches.length} 场比赛`;

    domElements.matchesList.innerHTML = validMatches.map(match => {
        const isWin = match.result === "win";
        const ratingChange = match.ratingChange || 0;
        const ratingChangeText = formatRatingChange(ratingChange);
        
        return `
            <div class="match-item">
                <div class="match-header">
                    <div class="match-result ${isWin ? 'result-win' : 'result-loss'}">
                        <div class="result-indicator ${isWin ? 'result-win' : 'result-loss'}"></div>
                        <span class="result-text">${isWin ? MATCH_RESULT_CONFIG.WIN_TEXT : MATCH_RESULT_CONFIG.LOSS_TEXT}</span>
                        ${match.hasHighDamage ? '<span class="high-damage-badge" title="单人伤害超过2000">🔥</span>' : ''}
                    </div>
                    <div class="rating-change ${ratingChange >= 0 ? 'rating-positive' : 'rating-negative'}">
                        ${ratingChangeText}
                    </div>
                </div>
                <div class="match-details">
                    <div class="detail-item">
                        <svg class="detail-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd"></path>
                            <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"></path>
                        </svg>
                        <a href="${EXTERNAL_LINKS.MATCH_DETAILS(match.match_id)}" target="_blank" class="match-id-link" title="点击查看比赛详情">
                            ${match.match_id}
                        </a>
                    </div>
                    <div class="detail-item">
                        <svg class="detail-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                        <span title="${getFullTimeString(match.date)}">${formatTime(match.date)}</span>
                    </div>
                    <div class="detail-item">
                        <svg class="detail-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span>${match.map}</span>
                    </div>
                    <div class="detail-item">
                        <svg class="detail-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                        <span>${formatDuration(match.duration)}</span>
                    </div>
                    ${match.totalDamage > 0 ? `
                    <div class="detail-item">
                        <svg class="detail-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="${match.hasHighDamage ? 'high-damage-text' : ''}" title="${match.hasHighDamage ? '单人伤害超过2000！' : '最高伤害'}">${match.totalDamage}</span>
                    </div>
                    ` : ''}
                </div>
            </div>
        `;
    }).join('');
}

/**
 * 显示结果区域
 */
function showResults() {
    domElements.results.style.display = 'block';
}

/**
 * 隐藏结果区域
 */
function hideResults() {
    domElements.results.style.display = 'none';
}

/**
 * 清除流式状态面板
 */
function clearStreamingStatus() {
    const statusDiv = document.getElementById('streaming-status');
    if (statusDiv) {
        statusDiv.remove();
    }
}

/**
 * 重置UI状态
 */
function resetUI() {
    hideResults();
    hideLoading();
    clearStreamingStatus();
    domElements.error.classList.remove('show');
    hideCorsNotice();
} 