<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强数据分析演示</title>
    <link rel="stylesheet" href="styles/main.css">
    <style>
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .demo-section {
            background: #fff;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .demo-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .data-input {
            width: 100%;
            height: 200px;
            font-family: monospace;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        
        .analyze-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
        }
        
        .analyze-btn:hover {
            background: #0056b3;
        }
        
        .results {
            margin-top: 20px;
        }
        
        .result-section {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .result-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #495057;
        }
        
        .result-content {
            font-family: monospace;
            white-space: pre-wrap;
            background: #fff;
            padding: 10px;
            border-radius: 3px;
            border: 1px solid #e9ecef;
        }
        
        .insight-item {
            background: #e3f2fd;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
        }
        
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }
        
        .metric-card {
            background: #fff;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #e9ecef;
            text-align: center;
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
        }
        
        .metric-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-section">
            <div class="demo-title">🔍 增强数据分析演示</div>
            <p>输入比赛数据JSON，点击分析按钮查看详细分析结果</p>
            
            <textarea class="data-input" id="dataInput" placeholder="请输入比赛数据JSON...">
{
  "type": "match",
  "data": {
    "fightId": 12345,
    "EndTime": 1703123456,
    "MapId": 5,
    "TotalPlayTimeInSec": 480,
    "ratingChange": 25,
    "Data": {
      "76561198012345678": {
        "DamageDealt": 2500,
        "Kills": 8,
        "Deaths": 2,
        "PlayTimeInSec": 480
      },
      "76561198087654321": {
        "DamageDealt": 1800,
        "Kills": 5,
        "Deaths": 4,
        "PlayTimeInSec": 480
      },
      "76561198011111111": {
        "DamageDealt": 1200,
        "Kills": 3,
        "Deaths": 6,
        "PlayTimeInSec": 480
      },
      "76561198022222222": {
        "DamageDealt": 800,
        "Kills": 2,
        "Deaths": 7,
        "PlayTimeInSec": 480
      }
    }
  }
}</textarea>
            
            <button class="analyze-btn" onclick="analyzeData()">🔍 分析数据</button>
        </div>
        
        <div class="results" id="results"></div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/enhanced-data-analysis.js"></script>
    
    <script>
        function analyzeData() {
            const dataInput = document.getElementById('dataInput').value;
            const resultsDiv = document.getElementById('results');
            
            try {
                // 解析输入数据
                const matchData = JSON.parse(dataInput);
                
                // 使用增强数据分析模块
                const analysis = window.enhancedDataAnalysis.analyzeMatchData(matchData);
                const formattedResult = window.enhancedDataAnalysis.formatAnalysisResult(analysis);
                
                // 显示结果
                displayResults(analysis, formattedResult);
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="demo-section">
                        <div class="result-title">❌ 分析失败</div>
                        <div class="result-content">错误: ${error.message}</div>
                    </div>
                `;
            }
        }
        
        function displayResults(analysis, formattedResult) {
            const resultsDiv = document.getElementById('results');
            
            resultsDiv.innerHTML = `
                <!-- 摘要 -->
                <div class="demo-section">
                    <div class="result-title">📊 比赛摘要</div>
                    <div class="metric-grid">
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.matchInfo}</div>
                            <div class="metric-label">比赛信息</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.playerCount}</div>
                            <div class="metric-label">参与玩家</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.intensity}</div>
                            <div class="metric-label">游戏强度</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.balance}</div>
                            <div class="metric-label">平衡性</div>
                        </div>
                    </div>
                </div>
                
                <!-- 玩家统计 -->
                <div class="demo-section">
                    <div class="result-title">👥 玩家统计</div>
                    <div class="metric-grid">
                        <div class="metric-card">
                            <div class="metric-value">${analysis.players.damageStats.totalDamage}</div>
                            <div class="metric-label">总伤害</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${analysis.players.damageStats.averageDamage}</div>
                            <div class="metric-label">平均伤害</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${analysis.players.damageStats.maxDamage}</div>
                            <div class="metric-label">最高伤害</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${analysis.players.killStats.totalKills}</div>
                            <div class="metric-label">总击杀</div>
                        </div>
                    </div>
                    
                    <div class="result-content">
玩家详情:
${analysis.players.playerDetails.map(player => 
    `玩家 ${player.playerId}: 伤害${player.damage}, 击杀${player.kills}, 死亡${player.deaths}, K/D${player.kdRatio}, 表现评分${player.performance}`
).join('\n')}
                    </div>
                </div>
                
                <!-- 游戏分析 -->
                <div class="demo-section">
                    <div class="result-title">🎮 游戏分析</div>
                    <div class="metric-grid">
                        <div class="metric-card">
                            <div class="metric-value">${analysis.gameplay.intensity}</div>
                            <div class="metric-label">游戏强度</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${analysis.gameplay.balance}</div>
                            <div class="metric-label">平衡性</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${analysis.gameplay.skillGap}</div>
                            <div class="metric-label">技能差距</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${analysis.gameplay.engagement}</div>
                            <div class="metric-label">参与度</div>
                        </div>
                    </div>
                </div>
                
                <!-- 性能指标 -->
                <div class="demo-section">
                    <div class="result-title">⚡ 性能指标</div>
                    <div class="metric-grid">
                        <div class="metric-card">
                            <div class="metric-value">${analysis.performance.efficiency.avgDamagePerMinute}</div>
                            <div class="metric-label">平均伤害/分钟</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${analysis.performance.efficiency.avgKillsPerMinute}</div>
                            <div class="metric-label">平均击杀/分钟</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${analysis.performance.efficiency.avgDamagePerKill}</div>
                            <div class="metric-label">平均伤害/击杀</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${analysis.performance.dominance.topPlayerShare}%</div>
                            <div class="metric-label">最高玩家占比</div>
                        </div>
                    </div>
                </div>
                
                <!-- 时间分析 -->
                <div class="demo-section">
                    <div class="result-title">⏰ 时间分析</div>
                    <div class="metric-grid">
                        <div class="metric-card">
                            <div class="metric-value">${analysis.timing.durationFormatted}</div>
                            <div class="metric-label">游戏时长</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${analysis.timing.timeOfDay}</div>
                            <div class="metric-label">一天中的时间</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${analysis.timing.dayOfWeek}</div>
                            <div class="metric-label">星期几</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${analysis.timing.gameSpeed}</div>
                            <div class="metric-label">游戏速度</div>
                        </div>
                    </div>
                </div>
                
                <!-- 地图分析 -->
                <div class="demo-section">
                    <div class="result-title">🗺️ 地图分析</div>
                    <div class="metric-grid">
                        <div class="metric-card">
                            <div class="metric-value">${analysis.map.mapName}</div>
                            <div class="metric-label">地图名称</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${analysis.map.mapType}</div>
                            <div class="metric-label">地图类型</div>
                        </div>
                    </div>
                </div>
                
                <!-- 洞察 -->
                <div class="demo-section">
                    <div class="result-title">💡 数据洞察</div>
                    ${formattedResult.insights.map(insight => 
                        `<div class="insight-item">${insight}</div>`
                    ).join('')}
                </div>
                
                <!-- 原始分析数据 -->
                <div class="demo-section">
                    <div class="result-title">📄 完整分析数据</div>
                    <div class="result-content">${JSON.stringify(analysis, null, 2)}</div>
                </div>
            `;
        }
        
        // 页面加载时自动分析示例数据
        window.addEventListener('load', function() {
            setTimeout(analyzeData, 1000);
        });
    </script>
</body>
</html>

