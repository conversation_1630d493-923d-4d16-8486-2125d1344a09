/**
 * 应用配置文件
 * 包含API端点、数据处理配置和常量定义
 */

// API 基础配置
const API_BASE = 'https://v2202406227732275300.nicesrv.de:5000';

// 数据处理配置
const DATA_CONFIG = {
    MAX_MATCHES_DISPLAY: 20,        // 最大显示比赛数量
    STREAMING_ENABLED: true,         // 启用流式数据处理
    UPDATE_INTERVAL: 10,             // 流式数据更新间隔
    TIMEOUT: 60000                   // 流式API超时时间（毫秒）
};

// DOM 元素 ID 常量
const DOM_IDS = {
    SEARCH_FORM: 'searchForm',
    STEAM_ID_INPUT: 'steamIdInput',
    SEARCH_BTN: 'searchBtn',
    LOADING: 'loading',
    ERROR: 'error',
    RESULTS: 'results',
    PLAYER_INFO: 'playerInfo',
    PLAYER_NAME: 'playerName',
    PLAYER_STATS: 'playerStats',
    MATCH_COUNT: 'matchCount',
    MATCHES_LIST: 'matchesList',
    CORS_NOTICE: 'corsNotice',
    PROGRESS_CONTAINER: 'progressContainer',
    PROGRESS_FILL: 'progressFill',
    PROGRESS_TEXT: 'progressText'
};

// API 端点配置
const API_ENDPOINTS = {
    PLAYER_STATS: (steamId) => `${API_BASE}/statistic/individualPlayerStats/${steamId}`,
    RECENT_MATCHES: (steamId) => `${API_BASE}/statistic/getRecentMatches/${steamId}`,
    FALLBACK_MATCHES: (steamId) => `${API_BASE}/database/getRecentMatches/${steamId}`
};

// 代理配置
const PROXY_CONFIG = {
    LOCAL: 'http://localhost:8080/proxy/',
    PUBLIC: 'https://cors-anywhere.herokuapp.com/'
};

// 错误消息配置
const ERROR_MESSAGES = {
    CORS_ERROR: 'CORS 错误：无法访问 API',
    PLAYER_NOT_FOUND: '未找到玩家数据，请检查 Steam ID 是否正确',
    SERVER_ERROR: '服务器内部错误，请稍后重试',
    NETWORK_ERROR: '网络连接失败，请检查网络连接',
    INVALID_STEAM_ID: '请输入正确的 Steam ID 格式（17位数字）',
    EMPTY_STEAM_ID: '请输入 Steam ID'
};

// 时间格式化配置
const TIME_CONFIG = {
    TIMEZONE_OFFSET: 8, // UTC+8
    DATE_FORMAT: {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    },
    FULL_DATE_FORMAT: {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        timeZone: 'Asia/Shanghai'
    }
};

// 比赛结果配置
const MATCH_RESULT_CONFIG = {
    WIN_THRESHOLD: 1,
    WIN_TEXT: '胜利',
    LOSS_TEXT: '失败',
    WIN_CLASS: 'result-win',
    LOSS_CLASS: 'result-loss'
};

// 外部链接配置
const EXTERNAL_LINKS = {
    MATCH_DETAILS: (matchId) => `https://ba-hub.net/statistics/matches/${matchId}`
};

// 将配置暴露到全局作用域，供其他模块使用
window.API_BASE = API_BASE;
window.DATA_CONFIG = DATA_CONFIG;
window.DOM_IDS = DOM_IDS;
window.API_ENDPOINTS = API_ENDPOINTS;
window.PROXY_CONFIG = PROXY_CONFIG;
window.ERROR_MESSAGES = ERROR_MESSAGES;
window.TIME_CONFIG = TIME_CONFIG;
window.MATCH_RESULT_CONFIG = MATCH_RESULT_CONFIG;
window.EXTERNAL_LINKS = EXTERNAL_LINKS;

// 添加配置加载确认
console.log('✅ 配置文件已加载，配置项:', {
    API_BASE: window.API_BASE,
    DATA_CONFIG: window.DATA_CONFIG,
    DOM_IDS: window.DOM_IDS,
    API_ENDPOINTS: window.API_ENDPOINTS,
    PROXY_CONFIG: window.PROXY_CONFIG,
    ERROR_MESSAGES: window.ERROR_MESSAGES,
    TIME_CONFIG: window.TIME_CONFIG,
    MATCH_RESULT_CONFIG: window.MATCH_RESULT_CONFIG,
    EXTERNAL_LINKS: window.EXTERNAL_LINKS
});

// 确保配置立即可用
if (typeof window.AppConfig === 'undefined') {
    window.AppConfig = {
        API_BASE,
        DATA_CONFIG,
        DOM_IDS,
        API_ENDPOINTS,
        PROXY_CONFIG,
        ERROR_MESSAGES,
        TIME_CONFIG,
        MATCH_RESULT_CONFIG,
        EXTERNAL_LINKS
    };
    console.log('✅ AppConfig 全局对象已创建');
} 