<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单位使用分析演示</title>
    <link rel="stylesheet" href="styles/main.css">
    <style>
        .demo-container {
            max-width: 95vw;
            margin: 0 auto;
            padding: 15px;
            min-width: 1200px;
        }
        
        .demo-section {
            background: #fff;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .demo-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .api-input-section {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .steam-id-input {
            flex: 1;
            min-width: 200px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .api-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
        }
        
        .api-btn:hover {
            background: #218838;
        }
        
        .api-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        

        
        .results {
            margin-top: 20px;
        }
        
        .result-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #495057;
        }
        
        .result-content {
            font-family: monospace;
            white-space: pre-wrap;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            border: 1px solid #e9ecef;
        }
        
        .insight-item {
            background: #e3f2fd;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
        }
        
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 12px;
            margin-top: 10px;
        }
        
        .metric-card {
            background: #fff;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #e9ecef;
            text-align: center;
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
        }
        
        .metric-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .unit-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .unit-table th,
        .unit-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .unit-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .player-unit-section {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .category-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-right: 5px;
        }
        
        .category-infantry { background: #e3f2fd; color: #1976d2; }
        .category-vehicle { background: #f3e5f5; color: #7b1fa2; }
        .category-aircraft { background: #e8f5e8; color: #388e3c; }
        .category-artillery { background: #fff3e0; color: #f57c00; }
        .category-support { background: #fce4ec; color: #c2185b; }
        .category-unknown { background: #f5f5f5; color: #616161; }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .loading.show {
            display: block;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }
        
        .info-message {
            background: #d1ecf1;
            color: #0c5460;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #bee5eb;
        }
        
        /* API状态样式 */
        .api-status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: 500;
        }
        
        .api-status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .api-status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .api-status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .match-selection-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .match-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }
        
        .match-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .match-item:hover {
            background-color: #f0f8ff;
        }
        
        .match-item.selected {
            background-color: #007bff;
            color: white;
        }
        
        .match-item:last-child {
            border-bottom: none;
        }
        
        .match-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .match-id {
            font-weight: bold;
            color: #007bff;
        }
        
        .match-date {
            font-size: 0.9em;
            color: #666;
        }
        
        .match-result {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .match-result.win {
            background: #28a745;
            color: white;
        }
        
        .match-result.loss {
            background: #dc3545;
            color: white;
        }
        
        .match-result.draw {
            background: #ffc107;
            color: #212529;
        }
        
        .match-input-section {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .match-id-input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .select-match-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
        }
        
        .select-match-btn:hover {
            background: #138496;
        }
        
        .select-match-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .no-matches {
            padding: 20px;
            text-align: center;
            color: #666;
            font-style: italic;
        }
        
        /* 队伍和玩家样式 */
        .team-section {
            margin: 20px 0;
            padding: 15px;
            border: 2px solid #007bff;
            border-radius: 12px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        
        .team-title {
            color: #007bff;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
            font-size: 1.5em;
        }
        
        .team-summary {
            margin-bottom: 15px;
            padding: 12px;
            background: rgba(0, 123, 255, 0.1);
            border-radius: 8px;
        }
        
        .player-unit-section {
            margin: 15px 0;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .player-title {
            color: #495057;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #dee2e6;
            font-size: 1.2em;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-section">
            <div class="demo-title">🎯 单位使用分析演示</div>
            <p>通过Steam ID从API获取数据，进行单位使用分析</p>
            
            <!-- API数据获取部分 -->
            <div class="api-input-section">
                <input type="text" class="steam-id-input" id="steamIdInput" placeholder="请输入17位Steam ID (例如: 76561198039354882)" maxlength="17">
                <button class="api-btn" id="fetchApiBtn" onclick="fetchDataFromAPI()">🚀 从API获取数据</button>
                <button class="api-btn" onclick="showProxyHelp()" style="background: #17a2b8;">🔧 网络问题帮助</button>
            </div>
            
            <div id="apiStatus"></div>
            
            <!-- 比赛选择部分 -->
            <div class="match-selection-section" id="matchSelectionSection" style="display: none;">
                <h4>🎮 选择要分析的比赛</h4>
                
                <!-- 通过比赛编号选择 -->
                <div class="match-input-section">
                    <input type="text" class="match-id-input" id="matchIdInput" placeholder="输入比赛编号 (例如: 2733311)">
                    <button class="select-match-btn" id="selectMatchBtn" onclick="selectMatchById()">🔍 选择比赛</button>
                </div>
                
                <!-- 比赛列表 -->
                <div class="match-list" id="matchList">
                    <!-- 比赛列表将在这里动态生成 -->
                </div>
                
                <div style="margin-top: 10px; font-size: 0.9em; color: #666;">
                    💡 提示：点击比赛列表中的任意比赛进行分析，或直接输入比赛编号
                </div>
            </div>
            

        </div>
        
        <div class="loading" id="loading">
            <div>🔄 正在从API获取数据...</div>
        </div>
        
        <div class="results" id="results"></div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/unit-analysis.js"></script>
    
    <script>
        // API配置
        const API_BASE = 'https://v2202406227732275300.nicesrv.de:5000';
        
        // 全局变量存储比赛数据
        let allMatches = [];
        let selectedMatch = null;
        
        // 从API获取数据
        async function fetchDataFromAPI() {
            const steamIdInput = document.getElementById('steamIdInput');
            const steamId = steamIdInput.value.trim();
            const fetchBtn = document.getElementById('fetchApiBtn');
            const loading = document.getElementById('loading');
            
            if (!steamId) {
                showApiStatus('请输入Steam ID', 'error');
                return;
            }
            
            // 显示加载状态
            fetchBtn.disabled = true;
            loading.classList.add('show');
            showApiStatus('正在获取数据...', 'info');
            
            try {
                // 定义API端点
                const API_BASE = 'https://v2202406227732275300.nicesrv.de:5000';
                const LOCAL_PROXY = 'http://localhost:8080/proxy/';
                
                // 尝试不同的请求方式
                const requestMethods = [
                    {
                        name: '直接请求',
                        url: `${API_BASE}/statistic/getRecentMatches/${steamId}`,
                        useProxy: false
                    },
                    {
                        name: '本地CORS代理',
                        url: `${LOCAL_PROXY}v2202406227732275300.nicesrv.de:5000/statistic/getRecentMatches/${steamId}`,
                        useProxy: true
                    }
                ];
                
                let lastError = null;
                
                for (const method of requestMethods) {
                    try {
                        console.log(`🔄 尝试 ${method.name}: ${method.url}`);
                        showApiStatus(`正在尝试 ${method.name}...`, 'info');
                        
                        // 创建 AbortController 用于超时控制
                        const controller = new AbortController();
                        const timeoutId = setTimeout(() => {
                            controller.abort();
                        }, 30000); // 30秒超时
                        
                        const response = await fetch(method.url, {
                            signal: controller.signal,
                            headers: {
                                'Accept': 'application/json, text/plain, */*'
                            }
                        });
                        
                        clearTimeout(timeoutId);
                        
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        
                        // 检查响应类型
                        const contentType = response.headers.get('content-type');
                        console.log(`📡 响应类型: ${contentType}`);
                        
                        // 获取原始文本
                        let rawText;
                        try {
                            rawText = await response.text();
                        } catch (textError) {
                            console.error('读取响应文本失败:', textError);
                            throw new Error('无法读取服务器响应');
                        }
                        
                        console.log(`📊 接收到 ${rawText.length} 字符的数据`);
                        console.log('API返回的原始数据前500字符:', rawText.substring(0, 500) + '...');
                        
                        if (!rawText || rawText.trim().length === 0) {
                            throw new Error('服务器返回空数据');
                        }
                        
                        // 解析多行JSON格式
                        const matches = parseMultiLineJSON(rawText);
                        
                        if (matches.length === 0) {
                            throw new Error('未找到有效的比赛数据');
                        }
                        
                        // 存储所有比赛数据
                        allMatches = matches;
                        
                        // 显示比赛选择界面
                        displayMatchSelection(matches);
                        
                        showApiStatus(`✅ 成功获取到 ${matches.length} 场比赛数据 (${method.name})`, 'success');
                        return; // 成功获取数据，退出循环
                        
                    } catch (error) {
                        console.error(`${method.name} 失败:`, error);
                        lastError = error;
                        
                        // 如果是超时或网络错误，继续尝试下一种方法
                        if (error.name === 'AbortError') {
                            console.log(`${method.name} 超时，尝试下一种方法`);
                            continue;
                        }
                        
                        // 如果是其他错误，也继续尝试
                        continue;
                    }
                }
                
                // 所有方法都失败了
                console.error('所有请求方法都失败了:', lastError);
                
                let errorMessage = '获取数据失败';
                if (lastError) {
                    if (lastError.message.includes('Failed to fetch') || lastError.message.includes('ERR_INCOMPLETE_CHUNKED_ENCODING')) {
                        errorMessage = '网络连接失败，请检查网络连接或API服务器状态。建议：1) 检查网络连接 2) 启动本地CORS代理服务器 3) 稍后重试';
                    } else if (lastError.message.includes('HTTP 404')) {
                        errorMessage = '未找到该Steam ID的比赛数据';
                    } else if (lastError.message.includes('HTTP 500')) {
                        errorMessage = '服务器内部错误，请稍后重试';
                    } else if (lastError.message.includes('AbortError')) {
                        errorMessage = '请求超时，请检查网络连接或稍后重试';
                    } else {
                        errorMessage = `请求失败: ${lastError.message}`;
                    }
                }
                
                showApiStatus(errorMessage, 'error');
                
            } finally {
                fetchBtn.disabled = false;
                loading.classList.remove('show');
            }
        }
        
        // 解析多行JSON格式
        function parseMultiLineJSON(rawText) {
            const lines = rawText.trim().split('\n');
            const matches = [];
            
            for (const line of lines) {
                if (line.trim()) {
                    try {
                        const data = JSON.parse(line);
                        if (data.type === 'match' && data.data) {
                            matches.push(data);
                        }
                    } catch (parseError) {
                        console.warn('跳过无效的JSON行:', line.substring(0, 100));
                    }
                }
            }
            
            return matches;
        }
        
        // 显示比赛选择界面
        function displayMatchSelection(matches) {
            const matchSelectionSection = document.getElementById('matchSelectionSection');
            const matchList = document.getElementById('matchList');
            
            // 显示比赛选择区域
            matchSelectionSection.style.display = 'block';
            
            if (matches.length === 0) {
                matchList.innerHTML = '<div class="no-matches">没有找到比赛数据</div>';
                return;
            }
            
            // 生成比赛列表
            const matchListHTML = matches.map((match, index) => {
                const matchData = match.data;
                const matchId = matchData.fightId || `Match_${index + 1}`;
                const endTime = matchData.EndTime ? new Date(matchData.EndTime * 1000) : new Date();
                const dateStr = endTime.toLocaleString('zh-CN');
                const result = matchData.result || 'unknown';
                const resultClass = result === 'win' ? 'win' : result === 'loss' ? 'loss' : 'draw';
                const resultText = result === 'win' ? '胜利' : result === 'loss' ? '失败' : '平局';
                
                return `
                    <div class="match-item" onclick="selectMatch(${index})" data-match-index="${index}">
                        <div class="match-info">
                            <div>
                                <span class="match-id">#${matchId}</span>
                                <span class="match-date">${dateStr}</span>
                            </div>
                            <span class="match-result ${resultClass}">${resultText}</span>
                        </div>
                    </div>
                `;
            }).join('');
            
            matchList.innerHTML = matchListHTML;
        }
        
        // 选择比赛
        function selectMatch(matchIndex) {
            // 移除之前的选中状态
            document.querySelectorAll('.match-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 添加选中状态
            const selectedItem = document.querySelector(`[data-match-index="${matchIndex}"]`);
            if (selectedItem) {
                selectedItem.classList.add('selected');
            }
            
            // 设置选中的比赛
            selectedMatch = allMatches[matchIndex];
            
            // 直接分析选中的比赛
            analyzeSelectedMatch();
            
            showApiStatus(`✅ 已选择比赛 #${selectedMatch.data.fightId || matchIndex + 1}`, 'success');
        }
        
        // 分析选中的比赛
        function analyzeSelectedMatch() {
            if (!selectedMatch) {
                const resultsDiv = document.getElementById('results');
                resultsDiv.innerHTML = `
                    <div class="demo-section">
                        <div class="result-title">❌ 分析失败</div>
                        <div class="result-content">请先选择一场比赛</div>
                    </div>
                `;
                return;
            }
            
            try {
                // 使用单位分析模块
                const unitAnalysis = window.unitAnalysis.analyzeUnitUsage(selectedMatch);
                const formattedResult = window.unitAnalysis.formatUnitAnalysis(unitAnalysis);
                
                // 显示结果
                displayUnitResults(unitAnalysis, formattedResult);
                
            } catch (error) {
                const resultsDiv = document.getElementById('results');
                resultsDiv.innerHTML = `
                    <div class="demo-section">
                        <div class="result-title">❌ 分析失败</div>
                        <div class="result-content">错误: ${error.message}</div>
                    </div>
                `;
            }
        }
        
        // 通过比赛编号选择比赛
        function selectMatchById() {
            const matchIdInput = document.getElementById('matchIdInput').value.trim();
            
            if (!matchIdInput) {
                showApiStatus('请输入比赛编号', 'error');
                return;
            }
            
            // 查找匹配的比赛
            const matchIndex = allMatches.findIndex(match => {
                const matchData = match.data;
                return matchData.fightId && matchData.fightId.toString() === matchIdInput;
            });
            
            if (matchIndex === -1) {
                showApiStatus(`未找到比赛编号为 ${matchIdInput} 的比赛`, 'error');
                return;
            }
            
            // 选择找到的比赛
            selectMatch(matchIndex);
        }
        
        // 显示API状态信息
        function showApiStatus(message, type = 'info') {
            const apiStatus = document.getElementById('apiStatus');
            if (!apiStatus) return;
            
            apiStatus.textContent = message;
            apiStatus.className = `api-status ${type}`;
            
            // 如果是错误状态，添加启动代理服务器的指导
            if (type === 'error' && message.includes('网络连接失败')) {
                setTimeout(() => {
                    const helpMessage = `
                        💡 解决网络问题的建议：
                        1. 确保网络连接正常
                        2. 启动本地CORS代理服务器：
                           - 打开命令行
                           - 运行: python cors-proxy.py
                        3. 或者尝试使用测试数据
                    `;
                    console.log(helpMessage);
                }, 2000);
            }
        }
        
        // 检查CORS代理服务器状态
        async function checkProxyServer() {
            try {
                const response = await fetch('http://localhost:8080/proxy/test', {
                    method: 'GET',
                    signal: AbortSignal.timeout(3000)
                });
                return response.ok;
            } catch (error) {
                return false;
            }
        }
        
        // 启动CORS代理服务器的指导
        function showProxyHelp() {
            const helpDiv = document.createElement('div');
            helpDiv.innerHTML = `
                <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin: 10px 0;">
                    <h4 style="margin: 0 0 10px 0; color: #495057;">🔧 启动CORS代理服务器</h4>
                    <p style="margin: 0 0 10px 0; color: #6c757d;">如果遇到网络连接问题，请按以下步骤启动本地代理服务器：</p>
                    <ol style="margin: 0; padding-left: 20px; color: #495057;">
                        <li>打开命令行或PowerShell</li>
                        <li>导航到项目目录</li>
                        <li>运行命令：<code>python cors-proxy.py</code></li>
                        <li>看到"🚀 CORS 代理服务器已启动"消息后，重新尝试获取数据</li>
                    </ol>
                    <div style="margin-top: 10px; padding: 8px; background: #e9ecef; border-radius: 4px; font-family: monospace; font-size: 0.9em;">
                        💻 快速启动命令：<br>
                        <code>cd /c/Users/<USER>/Desktop/base/2045 && python cors-proxy.py</code>
                    </div>
                    <button onclick="this.parentElement.remove()" style="margin-top: 10px; padding: 5px 10px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">关闭</button>
                </div>
            `;
            
            const container = document.querySelector('.demo-container');
            container.insertBefore(helpDiv, container.firstChild);
        }
        

        

        

        
        function displayUnitResults(unitAnalysis, formattedResult) {
            const resultsDiv = document.getElementById('results');
            
            // 按队伍分组玩家
            const teams = {};
            unitAnalysis.playerUnitDetails.forEach(player => {
                const teamId = player.teamId || 'unknown';
                if (!teams[teamId]) {
                    teams[teamId] = [];
                }
                teams[teamId].push(player);
            });
            
            resultsDiv.innerHTML = `
                <!-- 单位使用摘要 -->
                <div class="demo-section">
                    <div class="result-title">📊 单位使用摘要</div>
                    <div class="metric-grid">
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.totalUnits}</div>
                            <div class="metric-label">总单位数</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.uniqueUnits}</div>
                            <div class="metric-label">独特单位种类</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.averageUnitsPerPlayer.toFixed(1)}</div>
                            <div class="metric-label">平均单位/玩家</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.mostUsedUnit ? formattedResult.summary.mostUsedUnit.name : 'N/A'}</div>
                            <div class="metric-label">最常用单位</div>
                        </div>
                    </div>
                </div>
                
                <!-- 单位分类统计 -->
                <div class="demo-section">
                    <div class="result-title">🏷️ 单位分类统计</div>
                    <div class="metric-grid">
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.unitCategories.infantry}</div>
                            <div class="metric-label">步兵</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.unitCategories.vehicle}</div>
                            <div class="metric-label">车辆</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.unitCategories.aircraft}</div>
                            <div class="metric-label">飞机</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.unitCategories.artillery}</div>
                            <div class="metric-label">炮兵</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.unitCategories.support}</div>
                            <div class="metric-label">支援</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.unitCategories.unknown}</div>
                            <div class="metric-label">未知</div>
                        </div>
                    </div>
                </div>
                
                <!-- 最常用单位 -->
                <div class="demo-section">
                    <div class="result-title">🔥 最常用单位</div>
                    <table class="unit-table">
                        <thead>
                            <tr>
                                <th>排名</th>
                                <th>单位名称</th>
                                <th>使用次数</th>
                                <th>总伤害</th>
                                <th>总击杀</th>
                                <th>分类</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${formattedResult.details.mostUsedUnits.map((unit, index) => `
                                <tr>
                                    <td>${index + 1}</td>
                                    <td>${unit.name}</td>
                                    <td>${unit.usageCount}</td>
                                    <td>${unit.totalDamage.toFixed(1)}</td>
                                    <td>${unit.totalKills}</td>
                                    <td><span class="category-badge category-${unit.category}">${unit.category}</span></td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                
                <!-- 最高效单位 -->
                <div class="demo-section">
                    <div class="result-title">⚡ 最高效单位</div>
                    <table class="unit-table">
                        <thead>
                            <tr>
                                <th>排名</th>
                                <th>单位名称</th>
                                <th>效率评分</th>
                                <th>使用次数</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${formattedResult.details.unitEfficiency.mostEfficient.map((unit, index) => `
                                <tr>
                                    <td>${index + 1}</td>
                                    <td>${unit.name}</td>
                                    <td>${unit.efficiency.toFixed(0)}</td>
                                    <td>${unitAnalysis.unitUsage[unit.id]?.usageCount || 0}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                
                <!-- 按队伍分组的玩家单位统计 -->
                <div class="demo-section">
                    <div class="result-title">👥 按队伍分组的玩家单位统计</div>
                    ${Object.entries(teams).map(([teamId, players]) => `
                        <div class="team-section">
                            <h3 class="team-title">🏆 队伍 ${teamId}</h3>
                            <div class="team-summary">
                                <div class="metric-grid">
                                    <div class="metric-card">
                                        <div class="metric-value">${players.length}</div>
                                        <div class="metric-label">玩家数量</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-value">${players.reduce((sum, p) => sum + (formattedResult.details.playerUnitStats[p.playerId]?.totalUnits || 0), 0)}</div>
                                        <div class="metric-label">总单位数</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-value">${players.reduce((sum, p) => sum + (formattedResult.details.playerUnitStats[p.playerId]?.efficiency?.efficiency || 0), 0).toFixed(0)}</div>
                                        <div class="metric-label">总效率评分</div>
                                    </div>
                                </div>
                            </div>
                            
                            ${players.map(player => {
                                const playerStats = formattedResult.details.playerUnitStats[player.playerId];
                                if (!playerStats) return '';
                                
                                return `
                                    <div class="player-unit-section">
                                        <h4 class="player-title">👤 ${player.playerName} (ID: ${player.playerId})</h4>
                                        <div class="metric-grid">
                                            <div class="metric-card">
                                                <div class="metric-value">${playerStats.totalUnits}</div>
                                                <div class="metric-label">总单位数</div>
                                            </div>
                                            <div class="metric-card">
                                                <div class="metric-value">${playerStats.unitTypes.length}</div>
                                                <div class="metric-label">单位类型数</div>
                                            </div>
                                            <div class="metric-card">
                                                <div class="metric-value">${playerStats.efficiency.activeUnits}</div>
                                                <div class="metric-label">活跃单位</div>
                                            </div>
                                            <div class="metric-card">
                                                <div class="metric-value">${playerStats.efficiency.efficiency.toFixed(0)}</div>
                                                <div class="metric-label">效率评分</div>
                                            </div>
                                        </div>
                                        
                                        <h5>单位分类统计:</h5>
                                        <div class="metric-grid">
                                            ${Object.entries(playerStats.categoryStats).map(([category, stats]) => `
                                                <div class="metric-card">
                                                    <div class="metric-value">${stats.count}</div>
                                                    <div class="metric-label">${category} (伤害: ${stats.damage.toFixed(0)}, 击杀: ${stats.kills})</div>
                                                </div>
                                            `).join('')}
                                        </div>
                                        
                                        <h5>单位详细列表:</h5>
                                        <table class="unit-table">
                                            <thead>
                                                <tr>
                                                    <th>单位名称</th>
                                                    <th>数量</th>
                                                    <th>伤害</th>
                                                    <th>击杀</th>
                                                    <th>死亡</th>
                                                    <th>分类</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${Object.entries(playerStats.unitBreakdown).map(([unitId, unit]) => `
                                                    <tr>
                                                        <td>${unit.name}</td>
                                                        <td>${unit.count}</td>
                                                        <td>${unit.damage.toFixed(1)}</td>
                                                        <td>${unit.kills}</td>
                                                        <td>${unit.deaths.toFixed(1)}</td>
                                                        <td><span class="category-badge category-${unit.category}">${unit.category}</span></td>
                                                    </tr>
                                                `).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    `).join('')}
                </div>
                
                <!-- 单位使用洞察 -->
                <div class="demo-section">
                    <div class="result-title">💡 单位使用洞察</div>
                    ${formattedResult.insights.map(insight => 
                        `<div class="insight-item">${insight}</div>`
                    ).join('')}
                </div>
                
                <!-- 完整分析数据 -->
                <div class="demo-section">
                    <div class="result-title">📄 完整单位分析数据</div>
                    <div class="result-content">${JSON.stringify(unitAnalysis, null, 2)}</div>
                </div>
            `;
        }
        
        // 页面加载完成后的初始化
        window.addEventListener('load', async function() {
            console.log('🎯 单位分析演示页面已加载');
            
            // 检查CORS代理服务器状态
            const proxyRunning = await checkProxyServer();
            if (proxyRunning) {
                console.log('✅ CORS代理服务器正在运行');
                showApiStatus('✅ CORS代理服务器已就绪，可以直接使用API功能', 'success');
            } else {
                console.log('⚠️ CORS代理服务器未运行');
                showApiStatus('💡 提示：如果遇到网络问题，可以点击"🔧 网络问题帮助"按钮获取解决方案', 'info');
            }
        });
    </script>
</body>
</html>

