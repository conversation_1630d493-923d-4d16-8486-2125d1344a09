# 单位使用分析模块

## 概述

这个单位使用分析模块专门用于统计每个人每种单位的使用情况，提供详细的单位使用分析和洞察。

## 功能特性

### 🎯 单位使用统计
- **总单位数**: 统计所有玩家使用的单位总数
- **独特单位种类**: 统计不同单位类型的数量
- **单位分类**: 按步兵、车辆、飞机、炮兵、支援等分类统计
- **使用频率**: 统计每种单位的使用次数

### 👥 玩家单位分析
- **个人单位统计**: 每个玩家使用的单位详情
- **单位类型分布**: 玩家使用的单位类型分布
- **单位效率**: 计算每个玩家的单位使用效率
- **单位详细列表**: 每个玩家使用的具体单位及其表现

### 📊 单位表现分析
- **最常用单位**: 按使用次数排序的单位列表
- **最高效单位**: 按效率评分排序的单位列表
- **单位伤害统计**: 每种单位的总伤害和击杀数
- **单位效率评分**: 基于伤害和击杀的综合效率评分

### 💡 智能洞察
- **使用模式分析**: 分析玩家的单位使用模式
- **战术偏好**: 识别玩家的战术偏好
- **效率建议**: 基于数据提供效率改进建议

## 使用方法

### 1. 引入模块

```html
<script src="js/utils.js"></script>
<script src="js/unit-analysis.js"></script>
```

### 2. 分析单位数据

```javascript
// 准备包含单位数据的比赛数据
const matchData = {
    "type": "match",
    "data": {
        "fightId": 12345,
        "EndTime": 1703123456,
        "MapId": 5,
        "TotalPlayTimeInSec": 480,
        "ratingChange": 25,
        "Data": {
            "76561198012345678": {
                "Name": "玩家1",
                "DamageDealt": 2500,
                "Kills": 8,
                "Deaths": 2,
                "PlayTimeInSec": 480,
                "UnitData": {
                    "403371": {
                        "Id": 22,
                        "OptionIds": [9, 7],
                        "WasRefunded": true
                    },
                    "403384": {
                        "Id": 320,
                        "OptionIds": [1843],
                        "TotalDamageDealt": 105.24,
                        "TotalDamageReceived": 127.5,
                        "KilledCount": 2
                    }
                },
                "TransferredUnits": {
                    "403440": {
                        "Id": 31,
                        "OptionIds": [371]
                    }
                }
            }
        }
    }
};

// 执行单位分析
const unitAnalysis = window.unitAnalysis.analyzeUnitUsage(matchData);

// 格式化结果
const formattedResult = window.unitAnalysis.formatUnitAnalysis(unitAnalysis);
```

### 3. 查看结果

```javascript
// 单位使用摘要
console.log('单位使用摘要:', formattedResult.summary);

// 单位使用详情
console.log('单位使用详情:', formattedResult.details);

// 单位使用洞察
console.log('单位使用洞察:', formattedResult.insights);

// 获取特定玩家的单位统计
const playerStats = window.unitAnalysis.getPlayerUnitStats(unitAnalysis, '76561198012345678');

// 获取特定单位的使用统计
const unitStats = window.unitAnalysis.getUnitUsageStats(unitAnalysis, 320);

// 获取最常用单位
const mostUsedUnits = window.unitAnalysis.getMostUsedUnits(unitAnalysis, 10);

// 获取最高效单位
const mostEfficientUnits = window.unitAnalysis.getMostEfficientUnits(unitAnalysis, 5);
```

## 数据格式

### 输入数据格式

```javascript
{
    "type": "match",
    "data": {
        "fightId": number,           // 比赛ID
        "EndTime": number,           // 结束时间戳（秒）
        "MapId": number,             // 地图ID
        "TotalPlayTimeInSec": number, // 游戏时长（秒）
        "ratingChange": number,      // 评分变化
        "Data": {                    // 玩家数据
            "steamId": {
                "Name": string,      // 玩家名称
                "DamageDealt": number, // 总伤害
                "Kills": number,     // 总击杀
                "Deaths": number,    // 总死亡
                "PlayTimeInSec": number, // 游戏时长
                "UnitData": {        // 单位数据
                    "unitInstanceId": {
                        "Id": number,           // 单位ID
                        "OptionIds": [number],  // 选项ID数组
                        "WasRefunded": boolean, // 是否被退款
                        "TotalDamageDealt": number, // 单位造成的伤害
                        "TotalDamageReceived": number, // 单位受到的伤害
                        "KilledCount": number   // 单位击杀数
                    }
                },
                "TransferredUnits": { // 转移的单位
                    "unitInstanceId": {
                        "Id": number,
                        "OptionIds": [number]
                    }
                }
            }
        }
    }
}
```

### 输出数据格式

```javascript
{
    "totalUnits": number,
    "uniqueUnits": Set<number>,
    "unitUsage": {
        "unitId": {
            "id": number,
            "name": string,
            "category": string,
            "usageCount": number,
            "totalDamage": number,
            "totalKills": number,
            "totalDeaths": number,
            "players": [string]
        }
    },
    "playerUnitDetails": [
        {
            "playerId": string,
            "playerName": string,
            "units": [...],
            "totalUnits": number,
            "unitTypes": Set<string>,
            "unitEfficiency": {...}
        }
    ],
    "unitCategories": {
        "infantry": number,
        "vehicle": number,
        "aircraft": number,
        "artillery": number,
        "support": number,
        "unknown": number
    },
    "mostUsedUnits": [...],
    "unitEfficiency": {
        "mostEfficient": [...],
        "leastEfficient": [...],
        "averageEfficiency": number
    },
    "playerUnitStats": {
        "playerId": {
            "playerName": string,
            "totalUnits": number,
            "unitTypes": [string],
            "unitBreakdown": {...},
            "categoryStats": {...},
            "efficiency": {...}
        }
    }
}
```

## 单位分类

### 分类规则
- **infantry**: 步兵单位 (ID: 1-50)
- **vehicle**: 车辆单位 (ID: 51-150)
- **aircraft**: 飞机单位 (ID: 151-250)
- **artillery**: 炮兵单位 (ID: 251-350)
- **support**: 支援单位 (ID: 351-450)
- **unknown**: 未知单位 (其他ID)

### 自定义分类
可以通过修改 `categorizeUnit` 函数来自定义单位分类规则：

```javascript
function categorizeUnit(unitId) {
    // 自定义分类逻辑
    if (unitId >= 1 && unitId <= 50) return 'infantry';
    if (unitId >= 51 && unitId <= 150) return 'vehicle';
    // ... 更多分类
    return 'unknown';
}
```

## 效率计算

### 单位效率评分
单位效率评分基于以下公式：
```
效率 = (总伤害 + 击杀数 × 100) / 使用次数
```

### 玩家单位效率
- **总伤害**: 所有单位造成的总伤害
- **总击杀**: 所有单位的总击杀数
- **活跃单位**: 非退款且非转移的单位数量
- **伤害/单位**: 平均每个活跃单位的伤害
- **击杀/单位**: 平均每个活跃单位的击杀数
- **效率评分**: 综合效率评分

## 演示页面

访问 `unit-analysis-demo.html` 查看完整的演示效果，包括：
- 交互式数据输入
- 实时单位分析结果
- 可视化单位统计
- 玩家单位详情
- 单位效率排名
- 智能洞察生成

## 扩展功能

### 自定义单位名称
可以通过修改 `getUnitName` 函数来添加单位ID到名称的映射：

```javascript
function getUnitName(unitId) {
    const UNIT_NAMES = {
        1: "步枪兵",
        2: "机枪兵",
        22: "坦克",
        320: "战斗机",
        // ... 更多单位名称
    };
    return UNIT_NAMES[unitId] || `单位${unitId}`;
}
```

### 自定义效率计算
可以修改效率计算公式来适应不同的游戏需求：

```javascript
function calculatePlayerUnitEfficiency(units) {
    // 自定义效率计算逻辑
    const totalDamage = units.reduce((sum, unit) => sum + unit.damage, 0);
    const totalKills = units.reduce((sum, unit) => sum + unit.kills, 0);
    const activeUnits = units.filter(unit => !unit.wasRefunded && !unit.transferred);
    
    // 自定义效率公式
    const efficiency = activeUnits.length > 0 ? 
        (totalDamage * 0.7 + totalKills * 150) / activeUnits.length : 0;
    
    return {
        totalDamage: totalDamage,
        totalKills: totalKills,
        activeUnits: activeUnits.length,
        efficiency: efficiency.toFixed(0)
    };
}
```

## 注意事项

1. **数据完整性**: 确保输入数据包含 `UnitData` 字段
2. **单位ID映射**: 建议添加单位ID到名称的映射表
3. **分类规则**: 根据实际游戏调整单位分类规则
4. **效率计算**: 根据游戏平衡性调整效率计算公式

## 更新日志

### v1.0.0
- 初始版本发布
- 单位使用统计
- 玩家单位分析
- 单位效率计算
- 单位分类系统
- 智能洞察生成

