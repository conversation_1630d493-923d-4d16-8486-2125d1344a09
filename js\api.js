/**
 * API 服务模块
 * 包含所有API请求、流式数据处理和错误处理逻辑
 */

/**
 * 数据获取错误处理
 * @param {Error} error - 错误对象
 * @param {string} apiName - API名称
 * @throws {Error} 处理后的错误
 */
function handleApiError(error, apiName) {
    console.error(`${apiName} 请求失败:`, error);
    if (error.message.includes('CORS') || error.message.includes('Failed to fetch')) {
        throw new Error(ERROR_MESSAGES.CORS_ERROR);
    } else if (error.message.includes('404')) {
        throw new Error(ERROR_MESSAGES.PLAYER_NOT_FOUND);
    } else if (error.message.includes('500')) {
        throw new Error(ERROR_MESSAGES.SERVER_ERROR);
    } else {
        throw new Error(`${apiName} 请求失败：${error.message}`);
    }
}

/**
 * 获取玩家统计信息
 * @param {string} steamId - Steam ID
 * @returns {Promise<object>} 玩家统计数据
 */
async function getPlayerStats(steamId) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), DATA_CONFIG.TIMEOUT);
    
    try {
        // 尝试直接请求
        let response = await fetch(API_ENDPOINTS.PLAYER_STATS(steamId), {
            signal: controller.signal
        });
        clearTimeout(timeoutId);
        
        if (!response.ok) {
            throw new Error(`获取玩家统计信息失败: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log(`👤 获取到玩家统计信息: ${data?.name || '未知玩家'}`);
        return data;
    } catch (err) {
        clearTimeout(timeoutId);
        console.error('直接请求失败，尝试使用本地 CORS 代理:', err);
        
        // 如果直接请求失败，尝试使用本地 CORS 代理
        try {
            const localProxy = PROXY_CONFIG.LOCAL;
            const proxyResponse = await fetch(`${localProxy}${API_BASE.replace('https://', '')}/statistic/individualPlayerStats/${steamId}`, {
                signal: controller.signal
            });
            
            if (!proxyResponse.ok) {
                throw new Error(`通过本地代理获取玩家统计信息失败: ${proxyResponse.status}`);
            }
            
            const data = await proxyResponse.json();
            console.log(`👤 通过本地代理获取到玩家统计信息: ${data?.name || '未知玩家'}`);
            return data;
        } catch (proxyErr) {
            console.error('本地 CORS 代理请求也失败:', proxyErr);
            
            // 尝试使用公共 CORS 代理作为备选
            try {
                const corsProxy = PROXY_CONFIG.PUBLIC;
                const publicResponse = await fetch(`${corsProxy}${API_BASE}/statistic/individualPlayerStats/${steamId}`, {
                    signal: controller.signal
                });
                
                if (!publicResponse.ok) {
                    throw new Error(`通过公共代理获取玩家统计信息失败: ${publicResponse.status}`);
                }
                
                const data = await publicResponse.json();
                console.log(`👤 通过公共代理获取到玩家统计信息: ${data?.name || '未知玩家'}`);
                return data;
            } catch (publicProxyErr) {
                console.error('公共 CORS 代理请求也失败:', publicProxyErr);
                return null;
            }
        }
    }
}

/**
 * 获取比赛记录 - 流式API版本
 * @param {string} steamId - Steam ID
 * @returns {Promise<object>} 比赛记录数据
 */
async function getRecentMatches(steamId) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), DATA_CONFIG.TIMEOUT);
    
    try {
        console.log(`🚀 开始获取 Steam ID ${steamId} 的最新比赛记录（流式API）`);
        
        // 优先使用流式API获取最新数据
        let response = await fetch(API_ENDPOINTS.RECENT_MATCHES(steamId), {
            signal: controller.signal
        });
        clearTimeout(timeoutId);
        
        if (!response.ok) {
            throw new Error(`流式API请求失败: ${response.status} ${response.statusText}`);
        }
        
        // 检查是否支持流式读取
        const reader = response.body?.getReader();
        if (!reader) {
            throw new Error('响应不支持流式读取，回退到传统API');
        }
        
        console.log('📡 开始流式处理比赛数据...');
        return await processStreamingData(reader, steamId);
        
    } catch (err) {
        clearTimeout(timeoutId);
        console.error('流式API失败，尝试传统API:', err);
        
        // 回退到传统API
        return await getRecentMatchesFallback(steamId, controller);
    }
}

/**
 * 流式数据处理 - 改进版本，显示详细的API状态和数据解析
 * @param {ReadableStreamDefaultReader} reader - 流读取器
 * @param {string} steamId - Steam ID
 * @returns {Promise<object>} 处理后的比赛数据
 */
async function processStreamingData(reader, steamId) {
    const decoder = new TextDecoder();
    let matches = [];
    let totalMatches = 0;
    let processedCount = 0;
    let isComplete = false;
    let buffer = '';
    let chunkCount = 0;
    let totalBytes = 0;
    let parseErrors = 0;
    let successfulParses = 0;
    
    // 创建流数据状态监控面板
    const statusDiv = document.createElement('div');
    statusDiv.id = 'streaming-status';
    statusDiv.innerHTML = `
        <div style="background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%); border: 2px solid #4CAF50; border-radius: 12px; padding: 20px; margin: 15px 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                <div style="font-size: 24px; margin-right: 10px;">🌊</div>
                <h3 style="margin: 0; color: #2196F3; font-weight: 600;">流数据API实时监控</h3>
                <div id="connection-indicator" style="margin-left: auto; width: 12px; height: 12px; border-radius: 50%; background: #4CAF50; animation: pulse 2s infinite;"></div>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 15px;">
                <div style="background: white; padding: 12px; border-radius: 8px; border-left: 4px solid #2196F3;">
                    <div style="font-size: 12px; color: #666; margin-bottom: 4px;">API端点</div>
                    <div style="font-weight: bold; color: #333; font-size: 11px; word-break: break-all;">${API_ENDPOINTS.RECENT_MATCHES(steamId)}</div>
                </div>
                <div style="background: white; padding: 12px; border-radius: 8px; border-left: 4px solid #4CAF50;">
                    <div style="font-size: 12px; color: #666; margin-bottom: 4px;">目标Steam ID</div>
                    <div style="font-weight: bold; color: #333;">${steamId}</div>
                </div>
                <div style="background: white; padding: 12px; border-radius: 8px; border-left: 4px solid #FF9800;">
                    <div style="font-size: 12px; color: #666; margin-bottom: 4px;">数据块计数</div>
                    <div id="chunk-count" style="font-weight: bold; color: #333; font-size: 18px;">0</div>
                </div>
                <div style="background: white; padding: 12px; border-radius: 8px; border-left: 4px solid #9C27B0;">
                    <div style="font-size: 12px; color: #666; margin-bottom: 4px;">总接收字节</div>
                    <div id="total-bytes" style="font-weight: bold; color: #333; font-size: 18px;">0</div>
                </div>
                <div style="background: white; padding: 12px; border-radius: 8px; border-left: 4px solid #00BCD4;">
                    <div style="font-size: 12px; color: #666; margin-bottom: 4px;">已处理比赛</div>
                    <div id="processed-matches" style="font-weight: bold; color: #333; font-size: 18px;">0</div>
                </div>
                <div style="background: white; padding: 12px; border-radius: 8px; border-left: 4px solid #607D8B;">
                    <div style="font-size: 12px; color: #666; margin-bottom: 4px;">缓冲区大小</div>
                    <div id="buffer-size" style="font-weight: bold; color: #333;">0 字节</div>
                </div>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                <div style="background: white; padding: 12px; border-radius: 8px;">
                    <div style="font-size: 12px; color: #666; margin-bottom: 8px;">解析统计</div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>成功: <span id="successful-parses" style="color: #4CAF50; font-weight: bold;">0</span></span>
                        <span>失败: <span id="parse-errors" style="color: #f44336; font-weight: bold;">0</span></span>
                        <span>成功率: <span id="parse-rate" style="color: #2196F3; font-weight: bold;">100%</span></span>
                    </div>
                </div>
                <div style="background: white; padding: 12px; border-radius: 8px;">
                    <div style="font-size: 12px; color: #666; margin-bottom: 8px;">当前状态</div>
                    <div id="parse-status" style="color: #4CAF50; font-weight: bold; font-size: 14px;">🟢 连接中...</div>
                </div>
            </div>
            
            <div id="stream-errors" style="background: #ffebee; border-radius: 8px; padding: 12px; margin-bottom: 15px; display: none;">
                <div style="font-size: 12px; color: #666; margin-bottom: 8px;">❌ 错误日志</div>
                <div id="error-content" style="color: #f44336; font-size: 12px; max-height: 100px; overflow-y: auto;"></div>
            </div>
            
            <details style="background: white; border-radius: 8px; padding: 12px;">
                <summary style="cursor: pointer; font-weight: bold; color: #666; font-size: 12px;">🔍 详细调试日志</summary>
                <div id="stream-debug" style="margin-top: 10px; max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 12px; border-radius: 6px; font-family: 'Courier New', monospace; font-size: 11px; line-height: 1.4;"></div>
            </details>
        </div>
    `;
    
    // 插入到结果区域前面
    const resultsDiv = document.getElementById(DOM_IDS.RESULTS);
    resultsDiv.parentNode.insertBefore(statusDiv, resultsDiv);
    
    function updateStreamStatus() {
        document.getElementById('chunk-count').textContent = chunkCount;
        document.getElementById('total-bytes').textContent = totalBytes.toLocaleString();
        document.getElementById('processed-matches').textContent = processedCount;
        document.getElementById('buffer-size').textContent = buffer.length.toLocaleString() + ' 字节';
        document.getElementById('successful-parses').textContent = successfulParses;
        document.getElementById('parse-errors').textContent = parseErrors;
        
        const rate = successfulParses + parseErrors > 0 ? 
            Math.round((successfulParses / (successfulParses + parseErrors)) * 100) : 100;
        document.getElementById('parse-rate').textContent = rate + '%';
        document.getElementById('parse-rate').style.color = rate >= 90 ? '#4CAF50' : rate >= 70 ? '#FF9800' : '#f44336';
    }
    
    function addDebugLog(message, type = 'info') {
        const debugDiv = document.getElementById('stream-debug');
        const timestamp = new Date().toLocaleTimeString();
        const icons = { info: '📝', success: '✅', warning: '⚠️', error: '❌' };
        const colors = { info: '#333', success: '#4CAF50', warning: '#FF9800', error: '#f44336' };
        
        debugDiv.innerHTML += `<div style="color: ${colors[type]}; margin-bottom: 2px;">
            <span style="color: #666;">[${timestamp}]</span> ${icons[type]} ${message}
        </div>`;
        debugDiv.scrollTop = debugDiv.scrollHeight;
    }
    
    function updateParseStatus(status, type = 'success') {
        const statusElement = document.getElementById('parse-status');
        const icons = { success: '🟢', warning: '🟡', error: '🔴', processing: '🔄' };
        const colors = { success: '#4CAF50', warning: '#FF9800', error: '#f44336', processing: '#2196F3' };
        
        statusElement.innerHTML = `${icons[type]} ${status}`;
        statusElement.style.color = colors[type];
    }
    
    function showError(error) {
        const errorDiv = document.getElementById('stream-errors');
        const errorContent = document.getElementById('error-content');
        errorDiv.style.display = 'block';
        errorContent.innerHTML += `<div>${new Date().toLocaleTimeString()}: ${error}</div>`;
        
        // 同时在控制台显示错误，方便调试
        console.error(`[流式数据错误] ${new Date().toLocaleTimeString()}: ${error}`);
    }
    
    addDebugLog('🚀 开始流式数据处理，连接到API端点');
    updateParseStatus('正在连接...', 'processing');
    
    try {
        while (true) {
            const { done, value } = await reader.read();
            if (done) {
                addDebugLog('🏁 流式数据读取完成', 'success');
                updateParseStatus('读取完成', 'success');
                break;
            }
            
            chunkCount++;
            totalBytes += value.length;
            
            // 将新数据追加到缓冲区
            const chunk = decoder.decode(value, { stream: true });
            buffer += chunk;
            
            addDebugLog(`📦 接收数据块 #${chunkCount}: ${value.length} 字节，累计: ${totalBytes.toLocaleString()} 字节`);
            updateStreamStatus();
            
            // 尝试从缓冲区中提取完整的 JSON 对象
            let lines = buffer.split('\n');
            
            // 保留最后一行（可能不完整）
            const lastLine = lines.pop() || '';
            buffer = lastLine;
            
            for (const line of lines) {
                const trimmedLine = line.trim();
                if (!trimmedLine) continue;
                
                try {
                    updateParseStatus('解析JSON数据...', 'processing');
                    
                    // 尝试解析JSON
                    let data;
                    try {
                        data = JSON.parse(trimmedLine);
                        successfulParses++;
                        updateParseStatus('解析成功', 'success');
                        addDebugLog(`✅ JSON解析成功: ${data.type}`, 'success');
                    } catch (firstParseErr) {
                        // 记录详细的解析错误信息
                        addDebugLog(`❌ JSON解析失败: ${firstParseErr.message}`, 'error');
                        addDebugLog(`📄 问题数据: ${trimmedLine.substring(0, 100)}...`, 'error');
                        
                        // 如果直接解析失败，尝试修复
                        addDebugLog(`🔧 尝试修复JSON: ${trimmedLine.substring(0, 50)}...`, 'warning');
                        const fixedJson = tryFixJsonString(trimmedLine);
                        if (fixedJson) {
                            try {
                                data = JSON.parse(fixedJson);
                                successfulParses++;
                                updateParseStatus('修复后解析成功', 'success');
                                addDebugLog('✅ JSON修复并解析成功', 'success');
                            } catch (fixParseErr) {
                                addDebugLog(`❌ 修复后仍然解析失败: ${fixParseErr.message}`, 'error');
                                parseErrors++;
                                updateStreamStatus();
                                continue; // 跳过这一行，继续处理下一行
                            }
                        } else {
                            addDebugLog(`❌ 无法修复JSON，跳过此行`, 'error');
                            parseErrors++;
                            updateStreamStatus();
                            continue; // 跳过这一行，继续处理下一行
                        }
                    }
                    
                    // 处理不同类型的流式数据
                    switch (data.type) {
                        case "metadata":
                            totalMatches = data.totalMatches || 0;
                            addDebugLog(`🎯 收到元数据: ${totalMatches} 场比赛，Steam ID: ${data.steamId}`, 'info');
                            updateProgress(totalMatches, 0);
                            break;
                            
                        case "match":
                            if (data.data) {
                                matches.push(data.data);
                                processedCount++;
                                addDebugLog(`🎮 处理比赛数据 ${processedCount}/${totalMatches} - 比赛ID: ${data.data.fightId || 'N/A'}`, 'info');
                                updateProgress(totalMatches, processedCount);
                                updateStreamStatus();
                                
                                // 显示比赛详细信息
                                if (data.data.fightId) {
                                    const players = Object.keys(data.data.Data || {}).length;
                                    addDebugLog(`   📊 比赛详情: ID=${data.data.fightId}, 玩家数=${players}, 地图=${data.data.MapId || 'N/A'}`, 'info');
                                }
                                
                                // 增量更新UI
                                if (processedCount % 10 === 0 || processedCount === totalMatches) {
                                    addDebugLog(`🔄 触发增量渲染，当前比赛数量: ${matches.length}`, 'info');
                                    renderMatchesIncremental(matches);
                                }
                            } else {
                                addDebugLog(`⚠️ 收到空的比赛数据`, 'warning');
                            }
                            break;
                            
                        case "complete":
                            addDebugLog(`✅ 数据传输完成，总计处理 ${data.totalProcessed || processedCount} 场比赛`, 'success');
                            isComplete = true;
                            updateProgress(null, null);
                            updateParseStatus(`处理完成 (${processedCount} 场比赛)`, 'success');
                            break;
                            
                        case "error":
                            addDebugLog(`❌ 服务器错误: ${data.error}`, 'error');
                            showError(`服务器错误: ${data.error}`);
                            updateParseStatus('服务器错误', 'error');
                            throw new Error(data.error);
                            
                        default:
                            addDebugLog(`⚠️ 未知消息类型: ${data.type}`, 'warning');
                    }
                    
                    if (isComplete) break;
                    
                } catch (parseErr) {
                    parseErrors++;
                    updateParseStatus('解析失败', 'error');
                    addDebugLog(`❌ JSON解析失败: ${parseErr.message}`, 'error');
                    addDebugLog(`   原始数据片段: ${trimmedLine.substring(0, 100)}${trimmedLine.length > 100 ? '...' : ''}`, 'error');
                    showError(`解析错误: ${parseErr.message}`);
                    
                    // 智能恢复策略
                    if (trimmedLine.includes('{') && !trimmedLine.includes('}')) {
                        addDebugLog('🔄 检测到不完整JSON对象，添加到缓冲区等待更多数据', 'warning');
                        buffer = trimmedLine + (buffer ? '\n' + buffer : '');
                    } else if (trimmedLine.startsWith(':') || trimmedLine.startsWith(',') || trimmedLine.startsWith('"')) {
                        addDebugLog('🔄 检测到JSON片段，可能是数据截断，跳过此片段', 'warning');
                        continue;
                    } else if (trimmedLine.length > 10 && !trimmedLine.includes('{') && !trimmedLine.includes('}')) {
                        addDebugLog('🔄 检测到可能的数据损坏，尝试添加到缓冲区', 'warning');
                        buffer = trimmedLine + (buffer ? '\n' + buffer : '');
                    }
                    
                    updateStreamStatus();
                }
            }
            
            // 防止缓冲区过大
            if (buffer.length > 100000) {
                addDebugLog('⚠️ 缓冲区过大，进行清理以防止内存溢出', 'warning');
                const lastBraceIndex = buffer.lastIndexOf('{');
                if (lastBraceIndex > 0) {
                    buffer = buffer.substring(lastBraceIndex);
                    addDebugLog('🧹 缓冲区已清理，保留最后的JSON对象', 'info');
                } else {
                    buffer = '';
                    addDebugLog('🧹 缓冲区已完全清空', 'info');
                }
                updateStreamStatus();
            }
            
            if (isComplete) break;
        }
        
        // 处理缓冲区中剩余的数据
        if (buffer.trim()) {
            try {
                const data = JSON.parse(buffer.trim());
                if (data.type === "complete") {
                    addDebugLog('✅ 处理缓冲区中的完成信号', 'success');
                }
            } catch (parseErr) {
                addDebugLog(`⚠️ 缓冲区剩余数据解析失败: ${parseErr.message}`, 'warning');
            }
        }
        
        addDebugLog(`🏆 流式数据处理完成！统计信息:`, 'success');
        addDebugLog(`   📊 总比赛数: ${matches.length}`, 'success');
        addDebugLog(`   📦 数据块数: ${chunkCount}`, 'success');
        addDebugLog(`   💾 总字节数: ${totalBytes.toLocaleString()}`, 'success');
        addDebugLog(`   ✅ 解析成功: ${successfulParses}`, 'success');
        addDebugLog(`   ❌ 解析失败: ${parseErrors}`, 'success');
        
        updateParseStatus(`完成 (${matches.length} 场比赛)`, 'success');
        
        // 10秒后自动收起调试面板
        setTimeout(() => {
            const details = statusDiv.querySelector('details');
            if (details && details.open) {
                details.open = false;
                addDebugLog('📋 调试面板已自动收起', 'info');
            }
        }, 10000);
        
        return { match_stats: matches };
        
    } catch (error) {
        addDebugLog(`💥 流式处理发生严重错误: ${error.message}`, 'error');
        showError(`严重错误: ${error.message}`);
        updateParseStatus('处理失败', 'error');
        throw error;
    } finally {
        reader.releaseLock();
        addDebugLog('🔓 流式读取器已释放，连接已关闭', 'info');
        
        // 更新连接指示器
        const indicator = document.getElementById('connection-indicator');
        if (indicator) {
            indicator.style.background = '#9E9E9E';
            indicator.style.animation = 'none';
        }
    }
}

/**
 * 传统API备用方案
 * @param {string} steamId - Steam ID
 * @param {AbortController} controller - 中止控制器
 * @returns {Promise<object>} 比赛记录数据
 */
async function getRecentMatchesFallback(steamId, controller) {
    console.log('🔄 使用传统API作为备用方案');
    
    try {
        // 尝试直接请求
        let response = await fetch(API_ENDPOINTS.FALLBACK_MATCHES(steamId), {
            signal: controller.signal
        });
        
        if (!response.ok) {
            throw new Error(`获取比赛记录失败: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log(`📊 通过传统API获取到 ${data?.match_stats?.length || 0} 条比赛记录`);
        return data;
    } catch (err) {
        console.error('直接请求失败，尝试使用本地 CORS 代理:', err);
        
        // 如果直接请求失败，尝试使用本地 CORS 代理
        try {
            const localProxy = PROXY_CONFIG.LOCAL;
            const proxyResponse = await fetch(`${localProxy}${API_BASE.replace('https://', '')}/database/getRecentMatches/${steamId}`, {
                signal: controller.signal
            });
            
            if (!proxyResponse.ok) {
                throw new Error(`通过本地代理获取比赛记录失败: ${proxyResponse.status}`);
            }
            
            const data = await proxyResponse.json();
            console.log(`📊 通过本地代理获取到 ${data?.match_stats?.length || 0} 条比赛记录`);
            return data;
        } catch (proxyErr) {
            console.error('本地 CORS 代理请求也失败:', proxyErr);
            
            // 尝试使用公共 CORS 代理作为备选
            try {
                const corsProxy = PROXY_CONFIG.PUBLIC;
                const publicResponse = await fetch(`${corsProxy}${API_BASE}/database/getRecentMatches/${steamId}`, {
                    signal: controller.signal
                });
                
                if (!publicResponse.ok) {
                    throw new Error(`通过公共代理获取比赛记录失败: ${publicResponse.status}`);
                }
                
                const data = await publicResponse.json();
                console.log(`📊 通过公共代理获取到 ${data?.match_stats?.length || 0} 条比赛记录`);
                return data;
            } catch (publicProxyErr) {
                console.error('公共 CORS 代理请求也失败:', publicProxyErr);
                throw new Error('无法获取比赛记录。请确保本地 CORS 代理服务器正在运行，或联系管理员配置服务器 CORS 头。');
            }
        }
    }
} 