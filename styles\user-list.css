/* 用户列表专用样式 */

/* 导航链接样式 */
.nav-links {
    margin-top: 20px;
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.nav-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.9);
    color: #667eea;
    border-color: rgba(255, 255, 255, 0.5);
}

/* 控制区域样式 */
.controls-section {
    padding: 20px 30px; /* 减少padding，从30px 40px改为20px 30px */
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.controls-row {
    display: flex;
    gap: 15px; /* 减少gap，从20px改为15px */
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-between;
}

/* 刷新按钮 */
.refresh-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(40, 167, 69, 0.3);
}

.refresh-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.refresh-btn.loading .btn-icon {
    animation: spin 1s linear infinite;
}

/* 开始按钮样式 */
.start-btn {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.start-btn:hover {
    background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.start-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.start-btn.loading {
    background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
    cursor: not-allowed;
    pointer-events: none;
}

.start-btn.loading .btn-icon {
    animation: spin 1s linear infinite;
}

.start-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 排序控件 */
.sort-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.sort-controls label {
    font-weight: 600;
    color: #495057;
}

.sort-select {
    padding: 8px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    cursor: pointer;
}

.sort-order-btn {
    padding: 8px 12px;
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
}

.sort-order-btn:hover {
    background: #5a6268;
}

/* 过滤控件 */
.filter-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.filter-input {
    padding: 8px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    width: 200px;
}

.filter-input:focus {
    outline: none;
    border-color: #667eea;
}

.status-filter {
    padding: 8px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    cursor: pointer;
}

/* 用户统计样式 */
.user-stats {
    display: flex;
    gap: 25px; /* 减少gap，从30px改为25px */
    padding: 15px 30px; /* 减少padding，从20px 40px改为15px 30px */
    background: white;
    border-bottom: 1px solid #e9ecef;
    justify-content: center;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #495057;
}

.stat-value.online {
    color: #28a745;
}

.stat-value.recent {
    color: #ffc107;
}

.stat-value.offline {
    color: #6c757d;
}

/* 用户列表容器 */
.user-list-container {
    padding: 15px 30px 30px; /* 减少padding，从20px 40px 40px改为15px 30px 30px */
    background: white;
}

.user-list {
    display: grid;
    gap: 12px; /* 减少gap，从15px改为12px */
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)); /* 减少最小宽度，从350px改为320px */
}

/* 用户列表项 */
.user-item {
    display: flex;
    align-items: center;
    gap: 12px; /* 减少gap，从15px改为12px */
    padding: 12px; /* 减少padding，从15px改为12px */
    background: #f8f9fa;
    border-radius: 10px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.user-item:hover {
    background: #e9ecef;
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

/* 用户头像 */
.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.2rem;
    flex-shrink: 0;
}

/* 用户信息 */
.user-info {
    flex: 1;
    min-width: 0;
}

.user-name {
    font-weight: 600;
    font-size: 1rem;
    color: #495057;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-steam-id {
    font-size: 0.8rem;
    color: #6c757d;
    font-family: 'Courier New', monospace;
}

.user-last-online {
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 3px;
}

/* 用户状态 */
.user-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    flex-shrink: 0;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 0 2px #e9ecef;
}

.status-indicator.online {
    background: #28a745;
    box-shadow: 0 0 0 2px #28a745;
}

.status-indicator.recent {
    background: #ffc107;
    box-shadow: 0 0 0 2px #ffc107;
}

.status-indicator.offline {
    background: #6c757d;
    box-shadow: 0 0 0 2px #6c757d;
}

.status-text {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-text.online {
    color: #28a745;
}

.status-text.recent {
    color: #ffc107;
}

.status-text.offline {
    color: #6c757d;
}

/* 在线指示器动画 */
.status-indicator.online {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 2px #28a745, 0 0 0 4px rgba(40, 167, 69, 0.3);
    }
    50% {
        box-shadow: 0 0 0 2px #28a745, 0 0 0 8px rgba(40, 167, 69, 0.1);
    }
    100% {
        box-shadow: 0 0 0 2px #28a745, 0 0 0 4px rgba(40, 167, 69, 0.3);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .controls-row {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }
    
    .sort-controls,
    .filter-controls {
        justify-content: center;
    }
    
    .user-stats {
        gap: 20px;
        padding: 15px 20px;
    }
    
    .user-list-container {
        padding: 15px 20px 30px;
    }
    
    .user-list {
        grid-template-columns: 1fr;
    }
    
    .filter-input {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .controls-section {
        padding: 20px;
    }
    
    .user-item {
        padding: 12px;
        gap: 12px;
    }
    
    .user-avatar {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .nav-links {
        gap: 10px;
    }
    
    .nav-link {
        padding: 8px 16px;
        font-size: 0.9rem;
    }
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .user-list {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); /* 稍微减小最小宽度 */
    }
    
    .controls-section {
        padding: 15px 25px;
    }
    
    .user-list-container {
        padding: 12px 25px 25px;
    }
}

@media (max-width: 1200px) {
    .user-list {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 10px;
    }
    
    .user-item {
        padding: 10px;
        gap: 10px;
    }
    
    .user-avatar {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
    
    .controls-row {
        gap: 12px;
    }
}

@media (max-width: 1000px) {
    .user-list {
        grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
        gap: 8px;
    }
    
    .user-item {
        padding: 8px;
        gap: 8px;
    }
    
    .user-avatar {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .user-name {
        font-size: 0.9rem;
    }
    
    .user-steam-id {
        font-size: 0.75rem;
    }
    
    .user-stats {
        gap: 20px;
        padding: 12px 25px;
    }
    
    .stat-value {
        font-size: 1.3rem;
    }
    
    .stat-label {
        font-size: 0.8rem;
    }
}

/* 超宽屏幕优化 */
@media (min-width: 1600px) {
    .user-list {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); /* 在超宽屏幕上使用更大的卡片 */
        gap: 15px;
    }
    
    .user-item {
        padding: 15px;
        gap: 15px;
    }
    
    .user-avatar {
        width: 55px;
        height: 55px;
        font-size: 1.3rem;
    }
}