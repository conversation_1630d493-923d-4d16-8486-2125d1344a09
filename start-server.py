#!/usr/bin/env python3
"""
简单的本地 HTTP 服务器启动脚本
用于解决 CORS 问题
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

def start_server(port=8000):
    """启动本地 HTTP 服务器"""
    
    # 获取当前目录
    current_dir = Path(__file__).parent.absolute()
    
    # 切换到当前目录
    os.chdir(current_dir)
    
    # 创建服务器
    handler = http.server.SimpleHTTPRequestHandler
    
    try:
        with socketserver.TCPServer(("", port), handler) as httpd:
            print(f"🚀 本地服务器已启动")
            print(f"📁 服务目录: {current_dir}")
            print(f"🌐 访问地址: http://localhost:{port}")
            print(f"📄 比赛查询页面: http://localhost:{port}/index.html")
            print("\n按 Ctrl+C 停止服务器")
            
            # 自动打开浏览器
            try:
                webbrowser.open(f"http://localhost:{port}/index.html")
                print("🌍 已自动打开浏览器")
            except:
                print("⚠️  无法自动打开浏览器，请手动访问上述地址")
            
            # 启动服务器
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {port} 已被占用，请尝试其他端口")
            print(f"💡 可以运行: python start-server.py {port + 1}")
        else:
            print(f"❌ 启动服务器失败: {e}")
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")

if __name__ == "__main__":
    # 获取端口号（默认为 8000）
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("❌ 端口号必须是数字")
            sys.exit(1)
    
    start_server(port) 