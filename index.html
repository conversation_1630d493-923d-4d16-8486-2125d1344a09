<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>比赛记录查询</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎮</text></svg>">
    <link rel="stylesheet" href="styles/main.css">
    <style>
        /* 标签页样式 */
        .nav-links .nav-link {
            cursor: pointer;
        }
        
        .nav-links .nav-link.active {
            background-color: #007bff;
            color: white;
        }
        
        .tab-content {
            display: none !important;
        }
        
        .tab-content.active {
            display: block !important;
        }
        
        /* 单位分析样式 */
        .unit-analysis-section {
            background: #fff;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .api-input-section {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .steam-id-input {
            flex: 1;
            min-width: 200px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .api-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
        }
        
        .api-btn:hover {
            background: #218838;
        }
        
        .api-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        /* API状态样式 */
        .api-status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: 500;
        }
        
        .api-status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .api-status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .api-status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .match-selection-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .match-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }
        
        .match-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .match-item:hover {
            background-color: #f0f8ff;
        }
        
        .match-item.selected {
            background-color: #007bff;
            color: white;
        }
        
        .match-item:last-child {
            border-bottom: none;
        }
        
        .match-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .match-id {
            font-weight: bold;
            color: #007bff;
        }
        
        .match-date {
            font-size: 0.9em;
            color: #666;
        }
        
        .match-result {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .match-result.win {
            background: #28a745;
            color: white;
        }
        
        .match-result.loss {
            background: #dc3545;
            color: white;
        }
        
        .match-result.draw {
            background: #ffc107;
            color: #212529;
        }
        
        .match-input-section {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .match-id-input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .select-match-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
        }
        
        .select-match-btn:hover {
            background: #138496;
        }
        
        .select-match-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .no-matches {
            padding: 20px;
            text-align: center;
            color: #666;
            font-style: italic;
        }
        
        /* 单位分析结果样式 */
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 12px;
            margin-top: 10px;
        }
        
        .metric-card {
            background: #fff;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #e9ecef;
            text-align: center;
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
        }
        
        .metric-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .unit-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .unit-table th,
        .unit-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .unit-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .category-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-right: 5px;
        }
        
        .category-infantry { background: #e3f2fd; color: #1976d2; }
        .category-vehicle { background: #f3e5f5; color: #7b1fa2; }
        .category-aircraft { background: #e8f5e8; color: #388e3c; }
        .category-artillery { background: #fff3e0; color: #f57c00; }
        .category-support { background: #fce4ec; color: #c2185b; }
        .category-unknown { background: #f5f5f5; color: #616161; }
        
        /* 可排序表头样式 */
        .sortable {
            cursor: pointer;
            position: relative;
            user-select: none;
        }
        
        .sortable:hover {
            background-color: #e9ecef;
        }
        
        .sortable::after {
            content: '↕';
            position: absolute;
            right: 8px;
            color: #6c757d;
            font-size: 0.8em;
        }
        
        .sortable.asc::after {
            content: '↑';
            color: #007bff;
        }
        
        .sortable.desc::after {
            content: '↓';
            color: #007bff;
        }
        
        /* 单位链接样式 */
        .unit-link {
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
        }
        
        .unit-link:hover {
            color: #0056b3;
            text-decoration: underline;
        }
        
        .insight-item {
            background: #e3f2fd;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
        }
        
        /* 队伍和玩家样式 */
        .team-section {
            margin: 20px 0;
            padding: 15px;
            border: 2px solid #007bff;
            border-radius: 12px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        
        .team-title {
            color: #007bff;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
            font-size: 1.5em;
        }
        
        .team-summary {
            margin-bottom: 15px;
            padding: 12px;
            background: rgba(0, 123, 255, 0.1);
            border-radius: 8px;
        }
        
        .player-unit-section {
            margin: 15px 0;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .player-title {
            color: #495057;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #dee2e6;
            font-size: 1.2em;
        }
        
        .result-content {
            font-family: monospace;
            white-space: pre-wrap;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            border: 1px solid #e9ecef;
        }
        
        /* 单位分析结果样式 */
        .demo-section {
            background: #fff;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .result-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #495057;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 比赛记录查询</h1>
            <p>输入 Steam ID 查询玩家的比赛记录和统计信息</p>
            <!-- 导航链接 -->
            <div class="nav-links">
                <a href="index.html" class="nav-link active" data-tab="match-query">🎮 比赛记录查询</a>
                <a href="user-list.html" class="nav-link">👥 用户列表</a>
                <a href="#" class="nav-link" data-tab="unit-analysis">🎯 单位使用分析</a>
            </div>
        </div>

        <div class="search-section">
            <form class="search-form" id="searchForm">
                <input 
                    type="text" 
                    class="search-input" 
                    id="steamIdInput" 
                    placeholder="请输入 Steam ID (例如: 76561198012345678)"
                    required
                >
                <button type="submit" class="search-btn" id="searchBtn">
                    查询
                </button>
            </form>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在查询数据，请稍候...</p>
            <div class="progress-container" id="progressContainer" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <p class="progress-text" id="progressText">处理中...</p>
            </div>
        </div>

        <div class="error" id="error"></div>
        
        <!-- CORS 问题说明 -->
        <div class="cors-notice" id="corsNotice" style="display: none;">
            <div class="notice-content">
                <h3>⚠️ 跨域访问问题</h3>
                <p>由于浏览器的安全策略，直接打开 HTML 文件可能无法访问远程 API。请尝试以下解决方案：</p>
                <div class="solutions">
                    <div class="solution">
                        <h4>方案一：使用完整本地环境（推荐）</h4>
                        <p>Windows 用户：双击 <code>start-all.bat</code></p>
                        <p>其他系统：运行 <code>python start-server.py</code> 和 <code>python cors-proxy.py</code></p>
                        <p>然后访问：<code>http://localhost:8000/index.html</code></p>
                    </div>
                    <div class="solution">
                        <h4>方案二：使用浏览器扩展</h4>
                        <p>安装 CORS 浏览器扩展：</p>
                        <p>Chrome: "CORS Unblock" 或 "Allow CORS"</p>
                        <p>Firefox: "CORS Everywhere"</p>
                    </div>
                    <div class="solution">
                        <h4>方案三：联系管理员</h4>
                        <p>请管理员在 API 服务器上配置 CORS 头，允许跨域访问。</p>
                        <p>需要添加：<code>Access-Control-Allow-Origin: *</code></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 比赛记录查询标签页 -->
        <div class="tab-content active" id="match-query-tab">
            <div class="results" id="results" style="display: none;">
                <div class="player-info" id="playerInfo">
                    <h2 id="playerName">玩家信息</h2>
                    <div class="stats-grid" id="playerStats">
                        <!-- 统计信息将在这里显示 -->
                    </div>
                </div>

                <div class="matches-section">
                    <div class="matches-header">
                        <h3>最近比赛记录</h3>
                        <span class="match-count" id="matchCount">0 场比赛</span>
                    </div>
                    <div class="matches-list" id="matchesList">
                        <!-- 比赛记录将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 单位使用分析标签页 -->
        <div class="tab-content" id="unit-analysis-tab" style="display: none;">
            <div class="unit-analysis-section">
                <h2>🎯 单位使用分析</h2>
                <p>通过Steam ID从API获取数据，进行单位使用分析</p>
                
                <!-- API数据获取部分 -->
                <div class="api-input-section">
                    <input type="text" class="steam-id-input" id="unitSteamIdInput" placeholder="请输入17位Steam ID (例如: 76561198039354882)" maxlength="17">
                    <button class="api-btn" id="fetchApiBtn" onclick="fetchDataFromAPI()">🚀 从API获取数据</button>
                    <button class="api-btn" onclick="showProxyHelp()" style="background: #17a2b8;">🔧 网络问题帮助</button>
                </div>
                
                <div id="apiStatus"></div>
                
                <!-- 比赛选择部分 -->
                <div class="match-selection-section" id="matchSelectionSection" style="display: none;">
                    <h4>🎮 选择要分析的比赛</h4>
                    
                    <!-- 通过比赛编号选择 -->
                    <div class="match-input-section">
                        <input type="text" class="match-id-input" id="matchIdInput" placeholder="输入比赛编号 (例如: 2733311)">
                        <button class="select-match-btn" id="selectMatchBtn" onclick="selectMatchById()">🔍 选择比赛</button>
                    </div>
                    
                    <!-- 比赛列表 -->
                    <div class="match-list" id="matchList">
                        <!-- 比赛列表将在这里动态生成 -->
                    </div>
                    
                    <div style="margin-top: 10px; font-size: 0.9em; color: #666;">
                        💡 提示：点击比赛列表中的任意比赛进行分析，或直接输入比赛编号
                    </div>
                </div>
            </div>
            
            <div class="loading" id="unitLoading">
                <div>🔄 正在从API获取数据...</div>
            </div>
            
            <div class="results" id="unitResults"></div>
        </div>
    </div>

    <!-- 内联配置，确保立即可用 -->
    <script>
        // API 基础配置
        window.API_BASE = 'https://v2202406227732275300.nicesrv.de:5000';

        // 数据处理配置
        window.DATA_CONFIG = {
            MAX_MATCHES_DISPLAY: 100,
            STREAMING_ENABLED: true,
            UPDATE_INTERVAL: 10,
            TIMEOUT: 60000
        };

        // DOM 元素 ID 常量
        window.DOM_IDS = {
            SEARCH_FORM: 'searchForm',
            STEAM_ID_INPUT: 'steamIdInput',
            SEARCH_BTN: 'searchBtn',
            LOADING: 'loading',
            ERROR: 'error',
            RESULTS: 'results',
            PLAYER_INFO: 'playerInfo',
            PLAYER_NAME: 'playerName',
            PLAYER_STATS: 'playerStats',
            MATCH_COUNT: 'matchCount',
            MATCHES_LIST: 'matchesList',
            CORS_NOTICE: 'corsNotice',
            PROGRESS_CONTAINER: 'progressContainer',
            PROGRESS_FILL: 'progressFill',
            PROGRESS_TEXT: 'progressText'
        };

        // API 端点配置
        window.API_ENDPOINTS = {
            PLAYER_STATS: (steamId) => `${window.API_BASE}/statistic/individualPlayerStats/${steamId}`,
            RECENT_MATCHES: (steamId) => `${window.API_BASE}/statistic/getRecentMatches/${steamId}`,
            FALLBACK_MATCHES: (steamId) => `${window.API_BASE}/database/getRecentMatches/${steamId}`
        };

        // 代理配置
        window.PROXY_CONFIG = {
            LOCAL: 'http://localhost:8080/proxy/',
            PUBLIC: 'https://cors-anywhere.herokuapp.com/'
        };

        // 错误消息配置
        window.ERROR_MESSAGES = {
            CORS_ERROR: 'CORS 错误：无法访问 API',
            PLAYER_NOT_FOUND: '未找到玩家数据，请检查 Steam ID 是否正确',
            SERVER_ERROR: '服务器内部错误，请稍后重试',
            NETWORK_ERROR: '网络连接失败，请检查网络连接',
            INVALID_STEAM_ID: '请输入正确的 Steam ID 格式（17位数字）',
            EMPTY_STEAM_ID: '请输入 Steam ID'
        };

        // 时间格式化配置
        window.TIME_CONFIG = {
            DATE_FORMAT: {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                timeZone: 'Asia/Shanghai'
            },
            FULL_DATE_FORMAT: {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                timeZone: 'Asia/Shanghai'
            }
        };

        // 比赛结果配置
        window.MATCH_RESULT_CONFIG = {
            WIN_THRESHOLD: 1,
            WIN_TEXT: '胜利',
            LOSS_TEXT: '失败',
            WIN_CLASS: 'result-win',
            LOSS_CLASS: 'result-loss'
        };

        // 外部链接配置
        window.EXTERNAL_LINKS = {
            MATCH_DETAILS: (matchId) => `https://ba-hub.net/statistics/matches/${matchId}`
        };

        console.log('✅ 内联配置已加载');
    </script>

    <!-- JavaScript 文件 -->
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/events.js"></script>
    <script src="js/app.js"></script>
    <script src="js/unit-analysis.js"></script>
    
    <script>
        // 标签页切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');
            const tabContents = document.querySelectorAll('.tab-content');
            
            console.log('🔧 初始化标签页切换功能');
            console.log('导航链接数量:', navLinks.length);
            console.log('标签页内容数量:', tabContents.length);
            
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    const tabId = this.getAttribute('data-tab');
                    console.log('点击导航链接:', this.textContent, 'tabId:', tabId);
                    
                    // 如果是标签页切换（有data-tab属性），则阻止默认行为并切换标签页
                    if (tabId) {
                        e.preventDefault();
                        
                        // 移除所有活动状态
                        navLinks.forEach(l => l.classList.remove('active'));
                        tabContents.forEach(tab => tab.classList.remove('active'));
                        
                        // 添加活动状态
                        this.classList.add('active');
                        
                        // 显示对应的标签页内容
                        const targetTab = document.getElementById(tabId + '-tab');
                        if (targetTab) {
                            targetTab.classList.add('active');
                            console.log('✅ 切换到标签页:', tabId);
                        } else {
                            console.error('❌ 未找到标签页:', tabId + '-tab');
                        }
                    } else {
                        console.log('允许页面跳转:', this.href);
                    }
                    // 如果没有data-tab属性，则允许正常页面跳转（如user-list.html）
                });
            });
            
            // 确保默认显示第一个标签页
            const firstTab = document.querySelector('.tab-content.active');
            if (firstTab) {
                console.log('✅ 默认标签页已激活:', firstTab.id);
            } else {
                console.warn('⚠️ 没有默认激活的标签页');
                // 如果没有默认激活的标签页，手动激活第一个
                const firstTabContent = document.querySelector('.tab-content');
                if (firstTabContent) {
                    firstTabContent.classList.add('active');
                    console.log('🔧 手动激活第一个标签页:', firstTabContent.id);
                }
            }
            
            // 测试标签页切换功能
            console.log('🧪 测试标签页切换功能...');
            const unitAnalysisTab = document.getElementById('unit-analysis-tab');
            const unitAnalysisLink = document.querySelector('[data-tab="unit-analysis"]');
            if (unitAnalysisTab && unitAnalysisLink) {
                console.log('✅ 单位分析标签页元素存在');
            } else {
                console.error('❌ 单位分析标签页元素缺失');
            }
        });
        
        // 单位分析相关代码
        // API配置
        const UNIT_API_BASE = 'https://v2202406227732275300.nicesrv.de:5000';
        
                 // 全局变量存储比赛数据
         let allMatches = [];
         let selectedMatch = null;
         
         // 安全的数值处理函数
         function safeNumber(value, defaultValue = 0) {
             if (typeof value === 'number' && !isNaN(value)) {
                 return value;
             }
             return defaultValue;
         }
         
         // 安全的toFixed函数
         function safeToFixed(value, decimals = 0) {
             const num = safeNumber(value);
             return num.toFixed(decimals);
         }
         
         // 安全的队伍统计计算
         function safeTeamStats(players, formattedResult, statType) {
             try {
                 if (!Array.isArray(players) || players.length === 0) {
                     return 0;
                 }
                 
                 const result = players.reduce((sum, p) => {
                     const playerStats = formattedResult.details.playerUnitStats[p.playerId];
                     if (!playerStats) return sum;
                     
                     switch (statType) {
                         case 'totalUnits':
                             return sum + safeNumber(playerStats.totalUnits);
                         case 'efficiency':
                             return sum + safeNumber(playerStats.efficiency?.efficiency);
                         default:
                             return sum;
                     }
                 }, 0);
                 
                 return safeNumber(result);
             } catch (error) {
                 console.error('计算队伍统计时出错:', error);
                 return 0;
             }
         }
        
        // 从API获取数据
        async function fetchDataFromAPI() {
            const steamIdInput = document.getElementById('unitSteamIdInput');
            const steamId = steamIdInput.value.trim();
            const fetchBtn = document.getElementById('fetchApiBtn');
            const loading = document.getElementById('unitLoading');
            
            if (!steamId) {
                showApiStatus('请输入Steam ID', 'error');
                return;
            }
            
            // 显示加载状态
            fetchBtn.disabled = true;
            loading.style.display = 'block';
            showApiStatus('正在获取数据...', 'info');
            
            try {
                // 定义API端点
                const API_BASE = 'https://v2202406227732275300.nicesrv.de:5000';
                const LOCAL_PROXY = 'http://localhost:8080/proxy/';
                
                // 尝试不同的请求方式
                const requestMethods = [
                    {
                        name: '直接请求',
                        url: `${API_BASE}/statistic/getRecentMatches/${steamId}`,
                        useProxy: false
                    },
                    {
                        name: '本地CORS代理',
                        url: `${LOCAL_PROXY}v2202406227732275300.nicesrv.de:5000/statistic/getRecentMatches/${steamId}`,
                        useProxy: true
                    }
                ];
                
                let lastError = null;
                
                for (const method of requestMethods) {
                    try {
                        console.log(`🔄 尝试 ${method.name}: ${method.url}`);
                        showApiStatus(`正在尝试 ${method.name}...`, 'info');
                        
                        // 创建 AbortController 用于超时控制
                        const controller = new AbortController();
                        const timeoutId = setTimeout(() => {
                            controller.abort();
                        }, 30000); // 30秒超时
                        
                        const response = await fetch(method.url, {
                            signal: controller.signal,
                            headers: {
                                'Accept': 'application/json, text/plain, */*'
                            }
                        });
                        
                        clearTimeout(timeoutId);
                        
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        
                        // 检查响应类型
                        const contentType = response.headers.get('content-type');
                        console.log(`📡 响应类型: ${contentType}`);
                        
                        // 获取原始文本
                        let rawText;
                        try {
                            rawText = await response.text();
                        } catch (textError) {
                            console.error('读取响应文本失败:', textError);
                            throw new Error('无法读取服务器响应');
                        }
                        
                        console.log(`📊 接收到 ${rawText.length} 字符的数据`);
                        console.log('API返回的原始数据前500字符:', rawText.substring(0, 500) + '...');
                        
                        if (!rawText || rawText.trim().length === 0) {
                            throw new Error('服务器返回空数据');
                        }
                        
                        // 解析多行JSON格式
                        const matches = parseMultiLineJSON(rawText);
                        
                        if (matches.length === 0) {
                            throw new Error('未找到有效的比赛数据');
                        }
                        
                        // 存储所有比赛数据
                        allMatches = matches;
                        
                        // 显示比赛选择界面
                        displayMatchSelection(matches);
                        
                        showApiStatus(`✅ 成功获取到 ${matches.length} 场比赛数据 (${method.name})`, 'success');
                        return; // 成功获取数据，退出循环
                        
                    } catch (error) {
                        console.error(`${method.name} 失败:`, error);
                        lastError = error;
                        
                        // 如果是超时或网络错误，继续尝试下一种方法
                        if (error.name === 'AbortError') {
                            console.log(`${method.name} 超时，尝试下一种方法`);
                            continue;
                        }
                        
                        // 如果是其他错误，也继续尝试
                        continue;
                    }
                }
                
                // 所有方法都失败了
                console.error('所有请求方法都失败了:', lastError);
                
                let errorMessage = '获取数据失败';
                if (lastError) {
                    if (lastError.message.includes('Failed to fetch') || lastError.message.includes('ERR_INCOMPLETE_CHUNKED_ENCODING')) {
                        errorMessage = '网络连接失败，请检查网络连接或API服务器状态。建议：1) 检查网络连接 2) 启动本地CORS代理服务器 3) 稍后重试';
                    } else if (lastError.message.includes('HTTP 404')) {
                        errorMessage = '未找到该Steam ID的比赛数据';
                    } else if (lastError.message.includes('HTTP 500')) {
                        errorMessage = '服务器内部错误，请稍后重试';
                    } else if (lastError.message.includes('AbortError')) {
                        errorMessage = '请求超时，请检查网络连接或稍后重试';
                    } else {
                        errorMessage = `请求失败: ${lastError.message}`;
                    }
                }
                
                showApiStatus(errorMessage, 'error');
                
            } finally {
                fetchBtn.disabled = false;
                loading.style.display = 'none';
            }
        }
        
        // 解析多行JSON格式
        function parseMultiLineJSON(rawText) {
            const lines = rawText.trim().split('\n');
            const matches = [];
            
            for (const line of lines) {
                if (line.trim()) {
                    try {
                        const data = JSON.parse(line);
                        if (data.type === 'match' && data.data) {
                            matches.push(data);
                        }
                    } catch (parseError) {
                        console.warn('跳过无效的JSON行:', line.substring(0, 100));
                    }
                }
            }
            
            return matches;
        }
        
        // 显示比赛选择界面
        function displayMatchSelection(matches) {
            const matchSelectionSection = document.getElementById('matchSelectionSection');
            const matchList = document.getElementById('matchList');
            
            // 显示比赛选择区域
            matchSelectionSection.style.display = 'block';
            
            if (matches.length === 0) {
                matchList.innerHTML = '<div class="no-matches">没有找到比赛数据</div>';
                return;
            }
            
            // 生成比赛列表
            const matchListHTML = matches.map((match, index) => {
                const matchData = match.data;
                const matchId = matchData.fightId || `Match_${index + 1}`;
                const endTime = matchData.EndTime ? new Date(matchData.EndTime * 1000) : new Date();
                const dateStr = endTime.toLocaleString('zh-CN');
                const result = matchData.result || 'unknown';
                const resultClass = result === 'win' ? 'win' : result === 'loss' ? 'loss' : 'draw';
                const resultText = result === 'win' ? '胜利' : result === 'loss' ? '失败' : '平局';
                
                return `
                    <div class="match-item" onclick="selectMatch(${index})" data-match-index="${index}">
                        <div class="match-info">
                            <div>
                                <span class="match-id">#${matchId}</span>
                                <span class="match-date">${dateStr}</span>
                            </div>
                            <span class="match-result ${resultClass}">${resultText}</span>
                        </div>
                    </div>
                `;
            }).join('');
            
            matchList.innerHTML = matchListHTML;
        }
        
        // 选择比赛
        function selectMatch(matchIndex) {
            // 移除之前的选中状态
            document.querySelectorAll('.match-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 添加选中状态
            const selectedItem = document.querySelector(`[data-match-index="${matchIndex}"]`);
            if (selectedItem) {
                selectedItem.classList.add('selected');
            }
            
            // 设置选中的比赛
            selectedMatch = allMatches[matchIndex];
            
            // 直接分析选中的比赛
            analyzeSelectedMatch();
            
            showApiStatus(`✅ 已选择比赛 #${selectedMatch.data.fightId || matchIndex + 1}`, 'success');
        }
        
                 // 分析选中的比赛
         function analyzeSelectedMatch() {
             if (!selectedMatch) {
                 const resultsDiv = document.getElementById('unitResults');
                 resultsDiv.innerHTML = `
                     <div class="demo-section">
                         <div class="result-title">❌ 分析失败</div>
                         <div class="result-content">请先选择一场比赛</div>
                     </div>
                 `;
                 return;
             }
             
             try {
                 console.log('🔍 开始分析比赛数据...');
                 console.log('选中的比赛:', selectedMatch);
                 
                 // 使用单位分析模块
                 const unitAnalysis = window.unitAnalysis.analyzeUnitUsage(selectedMatch);
                 console.log('单位分析结果:', unitAnalysis);
                 
                 const formattedResult = window.unitAnalysis.formatUnitAnalysis(unitAnalysis);
                 console.log('格式化结果:', formattedResult);
                 
                 // 显示结果
                 displayUnitResults(unitAnalysis, formattedResult);
                 
                 // 初始化表格排序功能
                 setTimeout(() => {
                     initializeTableSorting();
                 }, 100);
                 
             } catch (error) {
                 console.error('分析比赛时出错:', error);
                 const resultsDiv = document.getElementById('unitResults');
                 resultsDiv.innerHTML = `
                     <div class="demo-section">
                         <div class="result-title">❌ 分析失败</div>
                         <div class="result-content">错误: ${error.message}<br><br>详细错误信息: ${error.stack}</div>
                     </div>
                 `;
             }
         }
        
        // 通过比赛编号选择比赛
        function selectMatchById() {
            const matchIdInput = document.getElementById('matchIdInput').value.trim();
            
            if (!matchIdInput) {
                showApiStatus('请输入比赛编号', 'error');
                return;
            }
            
            // 查找匹配的比赛
            const matchIndex = allMatches.findIndex(match => {
                const matchData = match.data;
                return matchData.fightId && matchData.fightId.toString() === matchIdInput;
            });
            
            if (matchIndex === -1) {
                showApiStatus(`未找到比赛编号为 ${matchIdInput} 的比赛`, 'error');
                return;
            }
            
            // 选择找到的比赛
            selectMatch(matchIndex);
        }
        
        // 显示API状态信息
        function showApiStatus(message, type = 'info') {
            const apiStatus = document.getElementById('apiStatus');
            if (!apiStatus) return;
            
            apiStatus.textContent = message;
            apiStatus.className = `api-status ${type}`;
            
            // 如果是错误状态，添加启动代理服务器的指导
            if (type === 'error' && message.includes('网络连接失败')) {
                setTimeout(() => {
                    const helpMessage = `
                        💡 解决网络问题的建议：
                        1. 确保网络连接正常
                        2. 启动本地CORS代理服务器：
                           - 打开命令行
                           - 运行: python cors-proxy.py
                        3. 或者尝试使用测试数据
                    `;
                    console.log(helpMessage);
                }, 2000);
            }
        }
        
        // 检查CORS代理服务器状态
        async function checkProxyServer() {
            try {
                const response = await fetch('http://localhost:8080/health', {
                    method: 'GET',
                    signal: AbortSignal.timeout(3000)
                });
                return response.ok;
            } catch (error) {
                // 如果连接被拒绝或其他错误，说明代理服务器没有运行
                return false;
            }
        }
        
        // 启动CORS代理服务器的指导
        function showProxyHelp() {
            const helpDiv = document.createElement('div');
            helpDiv.innerHTML = `
                <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin: 10px 0;">
                    <h4 style="margin: 0 0 10px 0; color: #495057;">🔧 启动CORS代理服务器</h4>
                    <p style="margin: 0 0 10px 0; color: #6c757d;">如果遇到网络连接问题，请按以下步骤启动本地代理服务器：</p>
                    <ol style="margin: 0; padding-left: 20px; color: #495057;">
                        <li>打开命令行或PowerShell</li>
                        <li>导航到项目目录</li>
                        <li>运行命令：<code>python cors-proxy.py</code></li>
                        <li>看到"🚀 CORS 代理服务器已启动"消息后，重新尝试获取数据</li>
                    </ol>
                    <div style="margin-top: 10px; padding: 8px; background: #e9ecef; border-radius: 4px; font-family: monospace; font-size: 0.9em;">
                        💻 快速启动命令：<br>
                        <code>cd /c/Users/<USER>/Desktop/base/2045 && python cors-proxy.py</code>
                    </div>
                    <button onclick="this.parentElement.remove()" style="margin-top: 10px; padding: 5px 10px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">关闭</button>
                </div>
            `;
            
            const container = document.querySelector('.unit-analysis-section');
            container.insertBefore(helpDiv, container.firstChild);
        }
        
                 function displayUnitResults(unitAnalysis, formattedResult) {
             try {
                 const resultsDiv = document.getElementById('unitResults');
                 
                 console.log('🔍 开始显示单位分析结果...');
                 console.log('单位分析数据:', unitAnalysis);
                 console.log('格式化结果:', formattedResult);
                 
                 // 验证数据完整性
                 if (!unitAnalysis || !formattedResult) {
                     throw new Error('单位分析数据或格式化结果为空');
                 }
                 
                 if (!unitAnalysis.playerUnitDetails || !Array.isArray(unitAnalysis.playerUnitDetails)) {
                     throw new Error('玩家单位详情数据无效');
                 }
                 
                 if (!formattedResult.details || !formattedResult.details.playerUnitStats) {
                     throw new Error('玩家单位统计数据无效');
                 }
                 
                 // 按队伍分组玩家
                 const teams = {};
                 unitAnalysis.playerUnitDetails.forEach(player => {
                     const teamId = player.teamId || 'unknown';
                     if (!teams[teamId]) {
                         teams[teamId] = [];
                     }
                     teams[teamId].push(player);
                 });
                 
                 console.log('队伍分组结果:', teams);
            
            resultsDiv.innerHTML = `
                <!-- 单位使用摘要 -->
                <div class="demo-section">
                    <div class="result-title">📊 单位使用摘要</div>
                    <div class="metric-grid">
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.totalUnits}</div>
                            <div class="metric-label">总单位数</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.uniqueUnits}</div>
                            <div class="metric-label">独特单位种类</div>
                        </div>
                                                 <div class="metric-card">
                             <div class="metric-value">${safeToFixed(formattedResult.summary.averageUnitsPerPlayer, 1)}</div>
                             <div class="metric-label">平均单位/玩家</div>
                         </div>
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.mostUsedUnit ? formattedResult.summary.mostUsedUnit.name : 'N/A'}</div>
                            <div class="metric-label">最常用单位</div>
                        </div>
                    </div>
                </div>
                
                <!-- 单位分类统计 -->
                <div class="demo-section">
                    <div class="result-title">🏷️ 单位分类统计</div>
                    <div class="metric-grid">
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.unitCategories.infantry}</div>
                            <div class="metric-label">步兵</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.unitCategories.vehicle}</div>
                            <div class="metric-label">车辆</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.unitCategories.aircraft}</div>
                            <div class="metric-label">飞机</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.unitCategories.artillery}</div>
                            <div class="metric-label">炮兵</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.unitCategories.support}</div>
                            <div class="metric-label">支援</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${formattedResult.summary.unitCategories.unknown}</div>
                            <div class="metric-label">未知</div>
                        </div>
                    </div>
                </div>
                
                <!-- 最常用单位 -->
                <div class="demo-section">
                    <div class="result-title">🔥 最常用单位</div>
                    <table class="unit-table">
                        <thead>
                            <tr>
                                <th>排名</th>
                                <th>单位名称</th>
                                <th>使用次数</th>
                                <th>总伤害</th>
                                <th>总击杀</th>
                                <th>分类</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${formattedResult.details.mostUsedUnits.map((unit, index) => `
                                <tr>
                                    <td>${index + 1}</td>
                                    <td>${unit.name}</td>
                                    <td>${unit.usageCount}</td>
                                                                         <td>${safeToFixed(unit.totalDamage, 1)}</td>
                                    <td>${unit.totalKills}</td>
                                    <td><span class="category-badge category-${unit.category}">${unit.category}</span></td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                
                <!-- 最高效单位 -->
                <div class="demo-section">
                    <div class="result-title">⚡ 最高效单位</div>
                    <table class="unit-table">
                        <thead>
                            <tr>
                                <th>排名</th>
                                <th>单位名称</th>
                                <th>效率评分</th>
                                <th>使用次数</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${formattedResult.details.unitEfficiency.mostEfficient.map((unit, index) => `
                                <tr>
                                    <td>${index + 1}</td>
                                    <td>${unit.name}</td>
                                                                         <td>${safeToFixed(unit.efficiency, 0)}</td>
                                    <td>${unitAnalysis.unitUsage[unit.id]?.usageCount || 0}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                
                <!-- 按队伍分组的玩家单位统计 -->
                <div class="demo-section">
                    <div class="result-title">👥 按队伍分组的玩家单位统计</div>
                    ${Object.entries(teams).map(([teamId, players]) => `
                        <div class="team-section">
                            <h3 class="team-title">🏆 队伍 ${teamId}</h3>
                            <div class="team-summary">
                                <div class="metric-grid">
                                    <div class="metric-card">
                                        <div class="metric-value">${players.length}</div>
                                        <div class="metric-label">玩家数量</div>
                                    </div>
                                                                         <div class="metric-card">
                                         <div class="metric-value">${safeTeamStats(players, formattedResult, 'totalUnits')}</div>
                                         <div class="metric-label">总单位数</div>
                                     </div>
                                     <div class="metric-card">
                                         <div class="metric-value">${safeToFixed(safeTeamStats(players, formattedResult, 'efficiency'), 0)}</div>
                                         <div class="metric-label">总效率评分</div>
                                     </div>
                                </div>
                            </div>
                            
                                                         ${players.map(player => {
                                 const playerStats = formattedResult.details.playerUnitStats[player.playerId];
                                 if (!playerStats) {
                                     console.warn(`未找到玩家 ${player.playerId} 的统计数据`);
                                     return '';
                                 }
                                 
                                 console.log(`处理玩家 ${player.playerId} 的统计数据:`, playerStats);
                                
                                return `
                                    <div class="player-unit-section">
                                        <h4 class="player-title">👤 ${player.playerName} (ID: ${player.playerId})</h4>
                                        <div class="metric-grid">
                                            <div class="metric-card">
                                                <div class="metric-value">${playerStats.totalUnits}</div>
                                                <div class="metric-label">总单位数</div>
                                            </div>
                                            <div class="metric-card">
                                                <div class="metric-value">${playerStats.unitTypes.length}</div>
                                                <div class="metric-label">单位类型数</div>
                                            </div>
                                            <div class="metric-card">
                                                <div class="metric-value">${playerStats.efficiency.activeUnits}</div>
                                                <div class="metric-label">活跃单位</div>
                                            </div>
                                            <div class="metric-card">
                                                <div class="metric-value">${safeToFixed(playerStats.efficiency.efficiency, 0)}</div>
                                                <div class="metric-label">效率评分</div>
                                            </div>
                                        </div>
                                        
                                        <h5>单位分类统计:</h5>
                                        <div class="metric-grid">
                                            ${Object.entries(playerStats.categoryStats).map(([category, stats]) => `
                                                <div class="metric-card">
                                                    <div class="metric-value">${stats.count}</div>
                                                    <div class="metric-label">${category} (伤害: ${safeToFixed(stats.damage, 0)}, 击杀: ${stats.kills})</div>
                                                </div>
                                            `).join('')}
                                        </div>
                                        
                                        <h5>单位详细列表:</h5>
                                        ${(() => {
                                            // 调试日志：检查每个单位的 damageReceived 值
                                            console.log(`🔍 玩家 ${playerStats.playerName} 的单位详细数据:`, playerStats.unitBreakdown);
                                            Object.entries(playerStats.unitBreakdown).forEach(([unitId, unit]) => {
                                                console.log(`🔍 单位 ${unit.name} (ID: ${unitId}) - damageReceived:`, unit.damageReceived, '类型:', typeof unit.damageReceived);
                                            });
                                            return '';
                                        })()}
                                        <table class="unit-table" data-player-id="${player.playerId}">
                                            <thead>
                                                <tr>
                                                    <th class="sortable" data-sort="name">单位名称</th>
                                                    <th class="sortable" data-sort="count">数量</th>
                                                    <th class="sortable" data-sort="damage">伤害</th>
                                                    <th class="sortable" data-sort="kills">击杀</th>
                                                    <th class="sortable" data-sort="damageReceived">受到伤害</th>
                                                    <th class="sortable" data-sort="damagePerCount">伤害/数量</th>
                                                    <th class="sortable" data-sort="damagePerReceived">伤害/受到伤害</th>
                                                    <th>分类</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${Object.entries(playerStats.unitBreakdown).map(([unitId, unit]) => {
                                                    const damagePerCount = unit.count > 0 ? unit.damage / unit.count : 0;
                                                    const damagePerReceived = unit.damageReceived > 0 ? unit.damage / unit.damageReceived : 0;
                                                    
                                                    // 构建单位链接，包含选项ID参数
                                                    let unitLink = `https://ba-hub.net/arsenal/${unitId}`;
                                                    if (unit.options && Array.isArray(unit.options) && unit.options.length > 0) {
                                                        const optionParams = unit.options.join('-');
                                                        unitLink += `?m=${optionParams}`;
                                                    }
                                                    
                                                    return `
                                                        <tr>
                                                            <td><a href="${unitLink}" target="_blank" class="unit-link">${unit.name}</a></td>
                                                            <td>${unit.count}</td>
                                                            <td>${safeToFixed(unit.damage, 1)}</td>
                                                            <td>${unit.kills}</td>
                                                            <td>${safeToFixed(unit.damageReceived, 1)}</td>
                                                            <td>${safeToFixed(damagePerCount, 1)}</td>
                                                            <td>${safeToFixed(damagePerReceived, 2)}</td>
                                                            <td><span class="category-badge category-${unit.category}">${unit.category}</span></td>
                                                        </tr>
                                                    `;
                                                }).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    `).join('')}
                </div>
                
                <!-- 单位使用洞察 -->
                <div class="demo-section">
                    <div class="result-title">💡 单位使用洞察</div>
                    ${formattedResult.insights.map(insight => 
                        `<div class="insight-item">${insight}</div>`
                    ).join('')}
                </div>
                
                <!-- 完整分析数据 -->
                <div class="demo-section">
                    <div class="result-title">📄 完整单位分析数据</div>
                                         <div class="result-content">${JSON.stringify(unitAnalysis, null, 2)}</div>
                 </div>
             `;
             
             } catch (error) {
                 console.error('显示单位分析结果时出错:', error);
                 const resultsDiv = document.getElementById('unitResults');
                 resultsDiv.innerHTML = `
                     <div class="demo-section">
                         <div class="result-title">❌ 显示结果失败</div>
                         <div class="result-content">
                             错误: ${error.message}<br><br>
                             详细错误信息: ${error.stack}<br><br>
                             单位分析数据: ${JSON.stringify(unitAnalysis, null, 2)}<br><br>
                             格式化结果: ${JSON.stringify(formattedResult, null, 2)}
                         </div>
                     </div>
                 `;
             }
         }
        
        // 表格排序功能
        function initializeTableSorting() {
            const tables = document.querySelectorAll('.unit-table');
            tables.forEach(table => {
                const headers = table.querySelectorAll('.sortable');
                headers.forEach(header => {
                    header.addEventListener('click', () => {
                        const sortKey = header.dataset.sort;
                        const currentOrder = header.classList.contains('asc') ? 'asc' : 
                                           header.classList.contains('desc') ? 'desc' : 'none';
                        
                        // 清除所有表头的排序状态
                        headers.forEach(h => {
                            h.classList.remove('asc', 'desc');
                        });
                        
                        // 设置新的排序状态
                        const newOrder = currentOrder === 'asc' ? 'desc' : 'asc';
                        header.classList.add(newOrder);
                        
                        // 执行排序
                        sortTable(table, sortKey, newOrder);
                    });
                });
                
                // 默认按伤害排序（降序）
                const damageHeader = table.querySelector('[data-sort="damage"]');
                if (damageHeader) {
                    damageHeader.classList.add('desc');
                    sortTable(table, 'damage', 'desc');
                }
            });
        }
        
        function sortTable(table, sortKey, order) {
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            
            rows.sort((a, b) => {
                let aValue, bValue;
                
                switch (sortKey) {
                    case 'name':
                        aValue = a.cells[0].textContent.trim();
                        bValue = b.cells[0].textContent.trim();
                        return order === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
                    
                    case 'count':
                        aValue = parseInt(a.cells[1].textContent) || 0;
                        bValue = parseInt(b.cells[1].textContent) || 0;
                        return order === 'asc' ? aValue - bValue : bValue - aValue;
                    
                    case 'damage':
                        aValue = parseFloat(a.cells[2].textContent) || 0;
                        bValue = parseFloat(b.cells[2].textContent) || 0;
                        return order === 'asc' ? aValue - bValue : bValue - aValue;
                    
                    case 'kills':
                        aValue = parseInt(a.cells[3].textContent) || 0;
                        bValue = parseInt(b.cells[3].textContent) || 0;
                        return order === 'asc' ? aValue - bValue : bValue - aValue;
                    
                    case 'damageReceived':
                        aValue = parseFloat(a.cells[4].textContent) || 0;
                        bValue = parseFloat(b.cells[4].textContent) || 0;
                        return order === 'asc' ? aValue - bValue : bValue - aValue;
                    
                    case 'damagePerCount':
                        aValue = parseFloat(a.cells[5].textContent) || 0;
                        bValue = parseFloat(b.cells[5].textContent) || 0;
                        return order === 'asc' ? aValue - bValue : bValue - aValue;
                    
                    case 'damagePerReceived':
                        aValue = parseFloat(a.cells[6].textContent) || 0;
                        bValue = parseFloat(b.cells[6].textContent) || 0;
                        return order === 'asc' ? aValue - bValue : bValue - aValue;
                    
                    default:
                        return 0;
                }
            });
            
            // 重新插入排序后的行
            rows.forEach(row => tbody.appendChild(row));
        }
        
        // 页面加载完成后的初始化
        window.addEventListener('load', async function() {
            console.log('🎯 单位分析功能已集成到主页面');
            
            // 检查CORS代理服务器状态
            const proxyRunning = await checkProxyServer();
            if (proxyRunning) {
                console.log('✅ CORS代理服务器正在运行');
                showApiStatus('✅ CORS代理服务器已就绪，可以直接使用API功能', 'success');
            } else {
                console.log('⚠️ CORS代理服务器未运行');
                showApiStatus('💡 提示：如果遇到网络问题，可以点击"🔧 网络问题帮助"按钮获取解决方案', 'info');
            }
        });
    </script>
</body>
</html>